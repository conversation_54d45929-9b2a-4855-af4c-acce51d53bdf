#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: 生命周期处理器 - 实现@PostConstruct和@PreDestroy注解的生命周期管理

生命周期处理器负责处理@PostConstruct和@PreDestroy注解标记的方法,
实现Bean生命周期管理功能.支持初始化后回调和销毁前回调.
"""

import inspect
from typing import Any

from loguru import logger

# 生命周期模块是必需依赖
from ..annotations.lifecycle import LifecycleManager
from .base import BeanPostProcessor, ProcessorOrder


def is_post_construct(obj: Any) -> bool:
    """检查对象是否有@PostConstruct注解"""
    return hasattr(obj, "__is_post_construct__") and obj.__is_post_construct__


def is_pre_destroy(obj: Any) -> bool:
    """检查对象是否有@PreDestroy注解"""
    return hasattr(obj, "__is_pre_destroy__") and obj.__is_pre_destroy__


class LifecycleAnnotationProcessor(BeanPostProcessor):
    """
    生命周期注解处理器

    负责处理@PostConstruct和@PreDestroy注解的生命周期管理,支持:
    - 初始化后回调:在Bean初始化完成后调用@PostConstruct方法
    - 销毁前回调:在Bean销毁前调用@PreDestroy方法
    - 方法调用顺序:支持多个生命周期方法的有序调用
    - 异常处理:生命周期方法异常不影响Bean创建

    处理器在Bean初始化后执行,确保所有依赖注入和配置完成后再调用生命周期方法.

    Example:
        # 生命周期方法
        class UserService:
            @PostConstruct
            def init(self):
                print("UserService initialized")
                self.setup_resources()

            @PreDestroy
            def cleanup(self):
                print("UserService cleanup")
                self.close_resources()
    """

    def __init__(self):
        """
        初始化生命周期处理器
        """
        self._lifecycle_manager = LifecycleManager()
        self._processed_beans: set[str] = set()
        self._lifecycle_cache: dict[type, tuple] = {}
        self._destroyed_beans: set[str] = set()

    def post_process_before_initialization(self, bean: Any, _bean_name: str) -> Any:
        """
        在Bean初始化前处理(生命周期处理器不需要前处理)

        Args:
            bean: Bean实例
            bean_name: Bean名称

        Returns:
            原始Bean实例
        """
        return bean

    def post_process_after_initialization(self, bean: Any, bean_name: str) -> Any:
        """
        在Bean初始化后处理@PostConstruct注解

        扫描Bean类中的@PostConstruct注解,执行初始化后回调.

        Args:
            bean: Bean实例
            bean_name: Bean名称

        Returns:
            处理后的Bean实例

        Raises:
            BeanProcessingError: 当生命周期方法调用失败时
        """
        if bean is None or bean_name in self._processed_beans:
            return bean

        logger.debug(f"Processing lifecycle for bean: {bean_name} (type: {type(bean).__name__})")

        try:
            # 执行@PostConstruct方法
            self._invoke_post_construct_methods(bean, bean_name)

            # 注册到生命周期管理器
            if self._lifecycle_manager:
                self._lifecycle_manager.manage_bean(bean)

            # 标记为已处理
            self._processed_beans.add(bean_name)

            logger.debug(f"Completed lifecycle initialization for bean: {bean_name}")
            return bean

        except Exception as e:
            # 生命周期方法异常不应阻止Bean创建,但需要记录
            logger.warning(f"Failed to process lifecycle methods for bean '{bean_name}': {e}")
            return bean

    def get_order(self) -> int:
        """
        获取处理器执行顺序

        生命周期处理器需要在所有其他处理器之后执行,
        确保依赖注入和配置完成后再调用生命周期方法.

        Returns:
            执行顺序(数字越小优先级越高)
        """
        return ProcessorOrder.LIFECYCLE_PROCESSOR

    def supports(self, bean: Any, _bean_name: str) -> bool:
        """
        检查是否支持处理指定的Bean

        Args:
            bean: Bean实例
            bean_name: Bean名称

        Returns:
            True表示支持处理此Bean
        """
        if bean is None:
            return False

        # 检查Bean类是否有生命周期注解
        return self._has_lifecycle_annotations(bean.__class__)

    def destroy_bean(self, bean: Any, bean_name: str) -> None:
        """
        销毁Bean时调用@PreDestroy方法

        Args:
            bean: Bean实例
            bean_name: Bean名称
        """
        if bean is None or bean_name in self._destroyed_beans:
            return

        try:
            # 执行@PreDestroy方法
            self._invoke_pre_destroy_methods(bean, bean_name)

            # 标记为已销毁
            self._destroyed_beans.add(bean_name)

            logger.debug(f"Completed lifecycle destruction for bean: {bean_name}")

        except Exception as e:
            # 销毁方法异常不应阻止Bean销毁,但需要记录
            logger.warning(f"Failed to destroy bean '{bean_name}': {e}")

    def _invoke_post_construct_methods(self, bean: Any, bean_name: str) -> None:
        """
        调用@PostConstruct标记的方法

        Args:
            bean: Bean实例
            bean_name: Bean名称
        """
        bean_class = bean.__class__

        # 获取缓存的生命周期方法信息
        post_construct_methods, _ = self._get_lifecycle_methods(bean_class)

        for method_name, method in post_construct_methods:
            try:
                # 调用@PostConstruct方法，支持异步方法
                import asyncio
                if asyncio.iscoroutinefunction(method):
                    # 异步方法需要在事件循环中执行
                    try:
                        loop = asyncio.get_event_loop()
                        if loop.is_running():
                            # 在运行中的事件循环中创建任务，但不等待完成
                            # 这样可以避免阻塞Bean初始化过程
                            task = loop.create_task(method(bean))
                            # 添加错误处理回调
                            task.add_done_callback(lambda t: logger.error(f"@PostConstruct method '{method_name}' failed: {t.exception()}") if t.exception() else None)
                            logger.debug(f"Created async task for @PostConstruct method '{method_name}' for bean '{bean_name}'")
                        else:
                            # 事件循环未运行，直接执行
                            loop.run_until_complete(method(bean))
                            logger.debug(f"Executed async @PostConstruct method '{method_name}' for bean '{bean_name}'")
                    except RuntimeError:
                        # 没有事件循环，创建新的
                        asyncio.run(method(bean))
                        logger.debug(f"Executed async @PostConstruct method '{method_name}' with new event loop for bean '{bean_name}'")
                else:
                    # 同步方法直接调用
                    method(bean)
                    logger.debug(f"Invoked @PostConstruct method '{method_name}' for bean '{bean_name}'")

            except Exception as e:
                # 记录错误但不阻止Bean创建
                logger.error(f"Error invoking @PostConstruct method '{method_name}' for bean '{bean_name}': {e}")
                # 可以选择是否抛出异常,这里选择继续处理
                # raise BeanCreationError(f"@PostConstruct method '{method_name}' failed", bean_name, e) from e

    def _invoke_pre_destroy_methods(self, bean: Any, bean_name: str) -> None:
        """
        调用@PreDestroy标记的方法

        Args:
            bean: Bean实例
            bean_name: Bean名称
        """
        bean_class = bean.__class__

        # 获取缓存的生命周期方法信息
        _, pre_destroy_methods = self._get_lifecycle_methods(bean_class)

        # 按相反顺序调用@PreDestroy方法
        for method_name, method in reversed(pre_destroy_methods):
            try:
                # 调用@PreDestroy方法，支持异步方法
                import asyncio
                if asyncio.iscoroutinefunction(method):
                    # 异步方法需要在事件循环中执行
                    try:
                        loop = asyncio.get_event_loop()
                        if loop.is_running():
                            # 在运行中的事件循环中，不能使用run_until_complete
                            # 需要使用同步等待方式处理异步@PreDestroy方法
                            import concurrent.futures
                            import threading

                            # 创建一个新线程来运行异步方法
                            def run_async_in_thread():
                                # 在新线程中创建新的事件循环
                                new_loop = asyncio.new_event_loop()
                                asyncio.set_event_loop(new_loop)
                                try:
                                    return new_loop.run_until_complete(method(bean))
                                finally:
                                    new_loop.close()

                            # 使用线程池执行异步方法
                            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                                future = executor.submit(run_async_in_thread)
                                try:
                                    future.result(timeout=30.0)  # 30秒超时
                                    logger.debug(f"Completed async @PreDestroy method '{method_name}' for bean '{bean_name}' in separate thread")
                                except concurrent.futures.TimeoutError:
                                    logger.warning(f"@PreDestroy method '{method_name}' for bean '{bean_name}' timed out after 30s")
                                except Exception as task_error:
                                    logger.error(f"Async @PreDestroy method '{method_name}' for bean '{bean_name}' failed: {task_error}")
                        else:
                            # 事件循环未运行，直接执行
                            loop.run_until_complete(method(bean))
                            logger.debug(f"Executed async @PreDestroy method '{method_name}' for bean '{bean_name}'")
                    except RuntimeError:
                        # 没有事件循环，创建新的
                        asyncio.run(method(bean))
                        logger.debug(f"Executed async @PreDestroy method '{method_name}' with new event loop for bean '{bean_name}'")
                else:
                    # 同步方法直接调用
                    method(bean)
                    logger.debug(f"Invoked @PreDestroy method '{method_name}' for bean '{bean_name}'")

            except Exception as e:
                # 记录错误但不阻止Bean销毁
                logger.error(f"Error invoking @PreDestroy method '{method_name}' for bean '{bean_name}': {e}")

    def _get_lifecycle_methods(self, bean_class: type) -> tuple[list[tuple], list[tuple]]:
        """
        获取类中的生命周期方法信息

        Args:
            bean_class: Bean类

        Returns:
            元组:(post_construct_methods, pre_destroy_methods)
        """
        if bean_class in self._lifecycle_cache:
            return self._lifecycle_cache[bean_class]

        post_construct_methods = []
        pre_destroy_methods = []

        logger.debug(f"Scanning lifecycle methods for class: {bean_class.__name__}")

        # 扫描类中的所有方法
        for attr_name in dir(bean_class):
            if attr_name.startswith("_"):
                continue

            try:
                attr = getattr(bean_class, attr_name)

                if inspect.isfunction(attr) or inspect.ismethod(attr):
                    # 检查@PostConstruct注解
                    if is_post_construct(attr):
                        post_construct_methods.append((attr_name, attr))
                        logger.debug(f"Found @PostConstruct method: {bean_class.__name__}.{attr_name}")

                    # 检查@PreDestroy注解
                    if is_pre_destroy(attr):
                        pre_destroy_methods.append((attr_name, attr))
                        logger.debug(f"Found @PreDestroy method: {bean_class.__name__}.{attr_name}")

            except Exception as e:
                logger.debug(f"Error scanning method {attr_name}: {e}")
                continue

        # 按方法名排序,确保调用顺序的一致性
        post_construct_methods.sort(key=lambda x: x[0])
        pre_destroy_methods.sort(key=lambda x: x[0])

        logger.debug(f"Class {bean_class.__name__} has {len(post_construct_methods)} @PostConstruct and {len(pre_destroy_methods)} @PreDestroy methods")

        # 缓存结果
        result = (post_construct_methods, pre_destroy_methods)
        self._lifecycle_cache[bean_class] = result
        return result

    def _has_lifecycle_annotations(self, bean_class: type) -> bool:
        """
        检查类是否有生命周期注解

        Args:
            bean_class: Bean类

        Returns:
            True表示有生命周期注解
        """
        # 检查方法级别的生命周期注解
        for attr_name in dir(bean_class):
            if attr_name.startswith("_"):
                continue

            try:
                attr = getattr(bean_class, attr_name)
                if (inspect.isfunction(attr) or inspect.ismethod(attr)) and (is_post_construct(attr) or is_pre_destroy(attr)):
                    return True
            except Exception:
                continue

        return False

    def get_managed_beans_count(self) -> int:
        """
        获取管理的Bean数量

        Returns:
            管理的Bean数量
        """
        return len(self._processed_beans)

    def get_destroyed_beans_count(self) -> int:
        """
        获取已销毁的Bean数量

        Returns:
            已销毁的Bean数量
        """
        return len(self._destroyed_beans)
