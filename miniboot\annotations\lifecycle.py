#!/usr/bin/env python
"""
* @author: cz
* @description: 生命周期注解实现

实现Mini-Boot框架的Bean生命周期管理注解,包括@PostConstruct、@PreDestroy等.
这些注解用于标记Bean的初始化和销毁方法,支持Bean生命周期的精确控制.
"""

import inspect
from typing import Callable, Optional, Union

from .metadata import OrderMetadata, PostConstructMetadata, PreDestroyMetadata


def PostConstruct(  # noqa: N802
    func: Optional[Callable] = None,
) -> Union[Callable, Callable[[Callable], Callable]]:
    """初始化方法注解装饰器

    标记一个方法在Bean实例化和依赖注入完成后执行.
    该方法会在Bean的所有依赖注入完成后自动调用.

    Args:
        func: 被装饰的方法

    Returns:
        装饰后的方法或装饰器函数

    Examples:
        @Component
        class UserService:
            @PostConstruct
            def init(self):
                print("UserService initialized")
                self.setup_resources()
    """

    def decorator(method: Callable) -> Callable:
        # 创建初始化方法元数据
        metadata = PostConstructMetadata(method=method.__name__)

        # 存储元数据
        method.__post_construct_metadata__ = metadata
        method.__is_post_construct__ = True
        method.__post_construct_method__ = method.__name__

        return method

    # 支持无参数调用
    if func is not None:
        return decorator(func)

    return decorator


def PreDestroy(  # noqa: N802
    func: Optional[Callable] = None,
) -> Union[Callable, Callable[[Callable], Callable]]:
    """销毁方法注解装饰器

    标记一个方法在Bean销毁前执行.
    该方法会在容器关闭或Bean被销毁前自动调用,用于清理资源.

    Args:
        func: 被装饰的方法

    Returns:
        装饰后的方法或装饰器函数

    Examples:
        @Component
        class DatabaseService:
            @PreDestroy
            def cleanup(self):
                print("DatabaseService cleanup")
                self.close_connections()
    """

    def decorator(method: Callable) -> Callable:
        # 创建销毁方法元数据
        metadata = PreDestroyMetadata(method=method.__name__)

        # 存储元数据
        method.__pre_destroy_metadata__ = metadata
        method.__is_pre_destroy__ = True
        method.__pre_destroy_method__ = method.__name__

        return method

    # 支持无参数调用
    if func is not None:
        return decorator(func)

    return decorator


def Order(  # noqa: N802
    value: int = 0,
) -> Callable[[Union[type, Callable]], Union[type, Callable]]:
    """顺序注解装饰器

    指定Bean的加载顺序或方法的执行顺序.
    数值越小优先级越高,默认为0.

    Args:
        value: 顺序值,数值越小优先级越高

    Returns:
        装饰器函数

    Examples:
        @Component
        @Order(1)
        class HighPriorityService:
            pass

        @Component
        @Order(10)
        class LowPriorityService:
            pass

        class EventHandler:
            @Order(1)
            @EventListener
            def handle_first(self, event):
                pass

            @Order(2)
            @EventListener
            def handle_second(self, event):
                pass
    """

    def decorator(target: Union[type, Callable]) -> Union[type, Callable]:
        # 创建顺序元数据
        metadata = OrderMetadata(value=value)

        # 存储元数据
        target.__order_metadata__ = metadata
        target.__is_ordered__ = True
        target.__order_value__ = value

        return target

    return decorator


# 工具函数
def is_post_construct(method: Callable) -> bool:
    """检查方法是否有@PostConstruct注解

    Args:
        method: 要检查的方法

    Returns:
        如果有@PostConstruct注解返回True,否则返回False
    """
    return hasattr(method, "__is_post_construct__") and method.__is_post_construct__


def is_pre_destroy(method: Callable) -> bool:
    """检查方法是否有@PreDestroy注解

    Args:
        method: 要检查的方法

    Returns:
        如果有@PreDestroy注解返回True,否则返回False
    """
    return hasattr(method, "__is_pre_destroy__") and method.__is_pre_destroy__


def get_post_construct_metadata(method: Callable) -> Optional[PostConstructMetadata]:
    """获取@PostConstruct注解元数据

    Args:
        method: 要获取元数据的方法

    Returns:
        PostConstruct元数据,如果没有则返回None
    """
    return getattr(method, "__post_construct_metadata__", None)


def get_pre_destroy_metadata(method: Callable) -> Optional[PreDestroyMetadata]:
    """获取@PreDestroy注解元数据

    Args:
        method: 要获取元数据的方法

    Returns:
        PreDestroy元数据,如果没有则返回None
    """
    return getattr(method, "__pre_destroy_metadata__", None)


def get_post_construct_method_name(method: Callable) -> Optional[str]:
    """获取@PostConstruct方法名

    Args:
        method: 要获取方法名的方法

    Returns:
        方法名,如果没有@PostConstruct注解则返回None
    """
    return getattr(method, "__post_construct_method__", None)


def get_pre_destroy_method_name(method: Callable) -> Optional[str]:
    """获取@PreDestroy方法名

    Args:
        method: 要获取方法名的方法

    Returns:
        方法名,如果没有@PreDestroy注解则返回None
    """
    return getattr(method, "__pre_destroy_method__", None)


def is_ordered(target: Union[type, Callable]) -> bool:
    """检查类或方法是否有@Order注解

    Args:
        target: 要检查的类或方法

    Returns:
        如果有@Order注解返回True,否则返回False
    """
    return hasattr(target, "__is_ordered__") and target.__is_ordered__


def get_order_metadata(target: Union[type, Callable]) -> Optional[OrderMetadata]:
    """获取@Order注解元数据

    Args:
        target: 要获取元数据的类或方法

    Returns:
        Order元数据,如果没有则返回None
    """
    return getattr(target, "__order_metadata__", None)


def get_order_value(target: Union[type, Callable]) -> int:
    """获取@Order注解的顺序值

    Args:
        target: 要获取顺序值的类或方法

    Returns:
        顺序值,如果没有@Order注解则返回0
    """
    return getattr(target, "__order_value__", 0)


def find_lifecycle_methods(cls: type) -> tuple[list[str], list[str]]:
    """查找类中的生命周期方法

    Args:
        cls: 要查找的类

    Returns:
        包含(post_construct_methods, pre_destroy_methods)的元组
    """
    post_construct_methods = []
    pre_destroy_methods = []

    # 遍历类的所有方法
    for name, method in inspect.getmembers(cls, predicate=inspect.isfunction):
        if is_post_construct(method):
            post_construct_methods.append(name)
        elif is_pre_destroy(method):
            pre_destroy_methods.append(name)

    # 也检查实例方法
    for name in dir(cls):
        if not name.startswith("_"):  # 跳过私有方法
            try:
                attr = getattr(cls, name)
                if callable(attr):
                    if is_post_construct(attr):
                        if name not in post_construct_methods:
                            post_construct_methods.append(name)
                    elif is_pre_destroy(attr) and name not in pre_destroy_methods:
                        pre_destroy_methods.append(name)
            except (AttributeError, TypeError):
                # 忽略无法访问的属性
                continue

    return post_construct_methods, pre_destroy_methods


def has_lifecycle_methods(cls: type) -> bool:
    """检查类是否有生命周期方法

    Args:
        cls: 要检查的类

    Returns:
        如果有生命周期方法返回True,否则返回False
    """
    post_construct_methods, pre_destroy_methods = find_lifecycle_methods(cls)
    return len(post_construct_methods) > 0 or len(pre_destroy_methods) > 0


def call_post_construct_methods(instance: object) -> None:
    """调用实例的所有@PostConstruct方法

    Args:
        instance: 要调用方法的实例
    """
    cls = instance.__class__
    post_construct_methods, _ = find_lifecycle_methods(cls)

    for method_name in post_construct_methods:
        try:
            method = getattr(instance, method_name)
            if callable(method):
                # 检查是否是异步方法
                import asyncio

                if asyncio.iscoroutinefunction(method):
                    # 异步方法需要在事件循环中执行
                    try:
                        loop = asyncio.get_event_loop()
                        if loop.is_running():
                            # 在运行中的事件循环中创建任务，但不等待完成
                            # 这样可以避免阻塞Bean初始化过程
                            task = loop.create_task(method())
                            # 添加错误处理回调
                            task.add_done_callback(
                                lambda t: print(f"@PostConstruct method '{method_name}' failed: {t.exception()}") if t.exception() else None
                            )
                            print(f"Created async task for @PostConstruct method '{method_name}'")
                        else:
                            # 事件循环未运行，直接执行
                            loop.run_until_complete(method())
                            print(f"Executed async @PostConstruct method '{method_name}'")
                    except RuntimeError:
                        # 没有事件循环，创建新的
                        asyncio.run(method())
                        print(f"Executed async @PostConstruct method '{method_name}' with new event loop")
                else:
                    # 同步方法直接调用
                    method()
        except Exception as e:
            # 在实际应用中,这里应该使用日志记录错误
            print(f"Error calling @PostConstruct method {method_name}: {e}")


def call_pre_destroy_methods(instance: object) -> None:
    """调用实例的所有@PreDestroy方法

    Args:
        instance: 要调用方法的实例
    """
    cls = instance.__class__
    _, pre_destroy_methods = find_lifecycle_methods(cls)

    for method_name in pre_destroy_methods:
        try:
            method = getattr(instance, method_name)
            if callable(method):
                # 检查是否为异步方法
                import asyncio

                if asyncio.iscoroutinefunction(method):
                    # 异步方法需要在事件循环中执行
                    try:
                        loop = asyncio.get_event_loop()
                        if loop.is_running():
                            # 在运行中的事件循环中，不能使用run_until_complete
                            # 需要使用同步等待方式处理异步@PreDestroy方法
                            import concurrent.futures

                            # 创建一个新线程来运行异步方法
                            def run_async_in_thread():
                                # 在新线程中创建新的事件循环
                                new_loop = asyncio.new_event_loop()
                                asyncio.set_event_loop(new_loop)
                                try:
                                    return new_loop.run_until_complete(method())
                                finally:
                                    new_loop.close()

                            # 使用线程池执行异步方法
                            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                                future = executor.submit(run_async_in_thread)
                                try:
                                    future.result(timeout=30.0)  # 30秒超时
                                    print(f"Completed async @PreDestroy method {method_name} in separate thread")
                                except concurrent.futures.TimeoutError:
                                    print(f"@PreDestroy method {method_name} timed out after 30s")
                                except Exception as task_error:
                                    print(f"Async @PreDestroy method {method_name} failed: {task_error}")
                        else:
                            # 事件循环未运行，直接执行
                            loop.run_until_complete(method())
                            print(f"Executed async @PreDestroy method {method_name}")
                    except RuntimeError:
                        # 没有事件循环，创建新的
                        asyncio.run(method())
                        # 静默执行，避免在正常关闭时产生噪音
                else:
                    # 同步方法直接调用
                    method()
        except Exception:
            # 静默处理异常，避免在关闭过程中产生噪音
            pass


def sort_by_order(*targets: Union[type, Callable]) -> list[Union[type, Callable]]:
    """根据@Order注解对目标进行排序

    Args:
        *targets: 要排序的类或方法列表

    Returns:
        按顺序值排序的列表,顺序值小的在前
    """
    return sorted(targets, key=get_order_value)


def sort_instances_by_order(*instances: object) -> list[object]:
    """根据实例类的@Order注解对实例进行排序

    Args:
        *instances: 要排序的实例列表

    Returns:
        按顺序值排序的实例列表,顺序值小的在前
    """
    return sorted(instances, key=lambda instance: get_order_value(instance.__class__))


def get_ordered_methods(cls: type, method_filter: Optional[Callable[[Callable], bool]] = None) -> list[tuple[str, Callable]]:
    """获取类中按@Order注解排序的方法

    Args:
        cls: 要查找的类
        method_filter: 方法过滤器函数,用于筛选特定的方法

    Returns:
        按顺序值排序的(方法名, 方法)元组列表
    """
    methods = []

    # 遍历类的所有方法
    for name, method in inspect.getmembers(cls, predicate=inspect.isfunction):
        if method_filter is None or method_filter(method):
            methods.append((name, method))

    # 也检查实例方法
    for name in dir(cls):
        if not name.startswith("_"):  # 跳过私有方法
            try:
                attr = getattr(cls, name)
                if callable(attr) and (name, attr) not in methods and (method_filter is None or method_filter(attr)):
                    methods.append((name, attr))
            except (AttributeError, TypeError):
                # 忽略无法访问的属性
                continue

    # 按Order注解排序
    return sorted(methods, key=lambda item: get_order_value(item[1]))


# Bean生命周期管理器
import atexit
import weakref
from typing import Any

from miniboot.utils import SingletonMeta


class LifecycleManager(metaclass=SingletonMeta):
    """Bean生命周期管理器

    负责管理所有Bean的生命周期,包括:
    - 在Bean创建后调用@PostConstruct方法
    - 在应用关闭时调用@PreDestroy方法
    - 跟踪所有需要生命周期管理的Bean实例
    使用单例模式确保全局唯一性.
    """

    def __init__(self):
        """初始化生命周期管理器"""
        if not hasattr(self, "_initialized"):
            # 使用弱引用存储Bean实例,避免内存泄漏
            self._managed_beans: weakref.WeakSet[Any] = weakref.WeakSet()

            # 注册应用退出时的清理函数
            atexit.register(self._cleanup_all_beans)

            # 统计信息
            self._stats = {"total_managed": 0, "post_construct_called": 0, "pre_destroy_called": 0, "errors": 0}

            self._initialized = True

    def manage_bean(self, bean_instance: Any) -> None:
        """管理Bean实例的生命周期

        Args:
            bean_instance: 要管理的Bean实例
        """
        if bean_instance is None:
            return

        # 检查是否有生命周期方法
        if not has_lifecycle_methods(bean_instance.__class__):
            return

        # 添加到管理列表
        self._managed_beans.add(bean_instance)
        self._stats["total_managed"] += 1

        # 调用@PostConstruct方法
        self._call_post_construct(bean_instance)

    def _call_post_construct(self, bean_instance: Any) -> None:
        """调用Bean的@PostConstruct方法

        Args:
            bean_instance: Bean实例
        """
        try:
            call_post_construct_methods(bean_instance)
            self._stats["post_construct_called"] += 1
        except Exception as e:
            self._stats["errors"] += 1
            # 在实际应用中应该使用日志记录
            print(f"Error in @PostConstruct for {bean_instance.__class__.__name__}: {e}")

    def _call_pre_destroy(self, bean_instance: Any) -> None:
        """调用Bean的@PreDestroy方法

        Args:
            bean_instance: Bean实例
        """
        try:
            call_pre_destroy_methods(bean_instance)
            self._stats["pre_destroy_called"] += 1
        except Exception as e:
            self._stats["errors"] += 1
            # 在实际应用中应该使用日志记录
            print(f"Error in @PreDestroy for {bean_instance.__class__.__name__}: {e}")

    def _cleanup_all_beans(self) -> None:
        """清理所有管理的Bean(应用退出时调用)"""
        # 创建Bean列表的副本,因为在清理过程中WeakSet可能会改变
        beans_to_cleanup = list(self._managed_beans)

        for bean_instance in beans_to_cleanup:
            self._call_pre_destroy(bean_instance)

    def get_managed_bean_count(self) -> int:
        """获取当前管理的Bean数量

        Returns:
            管理的Bean数量
        """
        return len(self._managed_beans)

    def get_stats(self) -> dict[str, int]:
        """获取生命周期管理统计信息

        Returns:
            包含统计信息的字典
        """
        current_stats = self._stats.copy()
        current_stats["current_managed"] = len(self._managed_beans)
        return current_stats

    def clear_stats(self) -> None:
        """清除统计信息"""
        self._stats = {"total_managed": 0, "post_construct_called": 0, "pre_destroy_called": 0, "errors": 0}


# 便捷函数已删除 - 请直接使用类构造器
# 使用方式：
# manager = LifecycleManager()
# manager.manage_bean(bean_instance)


# 所有便捷函数已删除 - 请直接使用类构造器
