#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 高性能依赖注入器 - 编译时优化和零反射机制
"""

import asyncio
import inspect
import threading
import time
from dataclasses import dataclass, field

# 导入基础依赖注入器（避免循环导入，使用TYPE_CHECKING）
from typing import TYPE_CHECKING, Any, Callable, Optional

from .definition import BeanDefinition


if TYPE_CHECKING:
    from .factory import DependencyInjector


class BeanDefinitionAdapter:
    """BeanDefinition适配器 - 为高性能注入器提供扩展属性"""

    def __init__(self, bean_definition: BeanDefinition, bean_factory=None):
        self._bean_definition = bean_definition
        self._bean_factory = bean_factory
        self._field_dependencies: dict[str, str] = {}
        self._method_dependencies: dict[str, list[str]] = {}

    def __getattr__(self, name):
        """代理到原始BeanDefinition"""
        return getattr(self._bean_definition, name)

    @property
    def bean_factory(self):
        """Bean工厂属性"""
        return self._bean_factory

    @bean_factory.setter
    def bean_factory(self, factory):
        """设置Bean工厂"""
        self._bean_factory = factory

    @property
    def field_dependencies(self) -> dict[str, str]:
        """字段依赖属性"""
        return self._field_dependencies

    @field_dependencies.setter
    def field_dependencies(self, deps: dict[str, str]):
        """设置字段依赖"""
        self._field_dependencies = deps

    @property
    def method_dependencies(self) -> dict[str, list[str]]:
        """方法依赖属性"""
        return self._method_dependencies

    @method_dependencies.setter
    def method_dependencies(self, deps: dict[str, list[str]]):
        """设置方法依赖"""
        self._method_dependencies = deps


@dataclass
class InjectionPlan:
    """依赖注入计划 - 编译时生成的注入指令"""

    bean_name: str
    bean_class: type

    # 构造函数注入计划
    constructor_dependencies: list[str] = field(default_factory=list)
    constructor_injection_code: Optional[str] = None

    # 字段注入计划
    field_dependencies: dict[str, str] = field(default_factory=dict)
    field_injection_code: Optional[str] = None

    # 方法注入计划
    method_dependencies: dict[str, list[str]] = field(default_factory=dict)
    method_injection_code: Optional[str] = None

    # 异步注入支持
    has_async_dependencies: bool = False
    async_injection_code: Optional[str] = None

    # 性能优化标记
    is_singleton: bool = True
    is_lazy: bool = False
    cache_dependencies: bool = True


class CompileTimeAnalyzer:
    """编译时依赖分析器 - 分析Bean类生成注入计划"""

    def __init__(self):
        self._analyzed_classes: dict[type, InjectionPlan] = {}
        self._dependency_graph: dict[str, set[str]] = {}

    def analyze_bean_class(self, bean_class: type, bean_name: str) -> InjectionPlan:
        """分析Bean类生成注入计划"""
        if bean_class in self._analyzed_classes:
            return self._analyzed_classes[bean_class]

        plan = InjectionPlan(bean_name=bean_name, bean_class=bean_class)

        # 分析构造函数依赖
        self._analyze_constructor(bean_class, plan)

        # 分析字段依赖
        self._analyze_fields(bean_class, plan)

        # 分析方法依赖
        self._analyze_methods(bean_class, plan)

        # 生成注入代码
        self._generate_injection_code(plan)

        # 缓存分析结果
        self._analyzed_classes[bean_class] = plan

        return plan

    def _analyze_constructor(self, bean_class: type, plan: InjectionPlan) -> None:
        """分析构造函数依赖"""
        try:
            signature = inspect.signature(bean_class.__init__)
            type_hints = self._get_type_hints(bean_class.__init__)

            for param_name, param in signature.parameters.items():
                if param_name == "self":
                    continue

                # 获取参数类型
                param_type = type_hints.get(param_name, param.annotation)
                if param_type != inspect.Parameter.empty:
                    dependency_name = self._resolve_dependency_name(param_type)
                    plan.constructor_dependencies.append(dependency_name)

        except Exception:
            # 构造函数分析失败，使用默认策略
            pass

    def _analyze_fields(self, bean_class: type, plan: InjectionPlan) -> None:
        """分析字段依赖"""
        # 分析类注解和@Autowired字段
        for attr_name in dir(bean_class):
            if attr_name.startswith("_"):
                continue

            try:
                attr = getattr(bean_class, attr_name)
                # 检查是否有@Autowired注解
                if hasattr(attr, "__annotations__") or hasattr(attr, "_autowired"):
                    dependency_name = self._resolve_field_dependency(attr_name, attr)
                    if dependency_name:
                        plan.field_dependencies[attr_name] = dependency_name
            except Exception:
                continue

    def _analyze_methods(self, bean_class: type, plan: InjectionPlan) -> None:
        """分析方法依赖"""
        for method_name in dir(bean_class):
            if method_name.startswith("_") or method_name in ["__init__", "__new__"]:
                continue

            try:
                method = getattr(bean_class, method_name)
                if callable(method) and hasattr(method, "_autowired"):
                    dependencies = self._analyze_method_dependencies(method)
                    if dependencies:
                        plan.method_dependencies[method_name] = dependencies
            except Exception:
                continue

    def _generate_injection_code(self, plan: InjectionPlan) -> None:
        """生成高性能注入代码"""
        # 生成构造函数注入代码
        if plan.constructor_dependencies:
            plan.constructor_injection_code = self._generate_constructor_code(plan)

        # 生成字段注入代码
        if plan.field_dependencies:
            plan.field_injection_code = self._generate_field_code(plan)

        # 生成方法注入代码
        if plan.method_dependencies:
            plan.method_injection_code = self._generate_method_code(plan)

        # 生成异步注入代码
        if plan.has_async_dependencies:
            plan.async_injection_code = self._generate_async_code(plan)

    def _generate_constructor_code(self, plan: InjectionPlan) -> str:
        """生成构造函数注入代码"""
        deps = ", ".join([f"factory.get_bean('{dep}')" for dep in plan.constructor_dependencies])
        return f"instance = {plan.bean_class.__name__}({deps})"

    def _generate_field_code(self, plan: InjectionPlan) -> str:
        """生成字段注入代码"""
        code_lines = []
        for field_name, dep_name in plan.field_dependencies.items():
            code_lines.append(f"instance.{field_name} = factory.get_bean('{dep_name}')")
        return "\n".join(code_lines)

    def _generate_method_code(self, plan: InjectionPlan) -> str:
        """生成方法注入代码"""
        code_lines = []
        for method_name, deps in plan.method_dependencies.items():
            deps_str = ", ".join([f"factory.get_bean('{dep}')" for dep in deps])
            code_lines.append(f"instance.{method_name}({deps_str})")
        return "\n".join(code_lines)

    def _generate_async_code(self, plan: InjectionPlan) -> str:
        """生成异步注入代码"""
        # 异步注入代码生成逻辑
        code_lines = [
            f"# Async injection code for {plan.bean_name}",
            "async def inject_async(factory):",
            f"    instance = {plan.bean_class.__name__}()",
        ]

        # 添加异步字段注入
        for field_name, dep_name in plan.field_dependencies.items():
            code_lines.append(f"    instance.{field_name} = await factory.get_bean_async('{dep_name}')")

        code_lines.append("    return instance")
        return "\n".join(code_lines)

    def _get_type_hints(self, func: Callable) -> dict[str, type]:
        """获取类型提示（带缓存）"""
        try:
            # Python 3.10+ 使用 get_annotations，否则使用 __annotations__
            if hasattr(inspect, "get_annotations"):
                return inspect.get_annotations(func)
            else:
                return getattr(func, "__annotations__", {})
        except Exception:
            return {}

    def _resolve_dependency_name(self, param_type: type) -> str:
        """解析依赖名称"""
        if hasattr(param_type, "__name__"):
            return param_type.__name__.lower()
        return str(param_type).lower()

    def _resolve_field_dependency(self, field_name: str, field_value: Any) -> Optional[str]:
        """解析字段依赖"""
        # 简化实现，实际应该更复杂
        # 检查字段值是否有类型信息
        if hasattr(field_value, "__class__") and hasattr(field_value.__class__, "__name__"):
            return field_value.__class__.__name__.lower()
        return field_name.lower()

    def _analyze_method_dependencies(self, method: Callable) -> list[str]:
        """分析方法依赖"""
        # 简化实现，分析方法参数
        try:
            signature = inspect.signature(method)
            dependencies = []
            for param_name, param in signature.parameters.items():
                if param_name != "self" and param.annotation != inspect.Parameter.empty:
                    dep_name = self._resolve_dependency_name(param.annotation)
                    dependencies.append(dep_name)
            return dependencies
        except Exception:
            return []


class ZeroReflectionInjector:
    """零反射注入器 - 使用编译时生成的代码进行注入"""

    def __init__(self, analyzer: CompileTimeAnalyzer):
        self._analyzer = analyzer
        self._compiled_injectors: dict[str, Callable] = {}
        self._injection_stats = {
            "zero_reflection_injections": 0,
            "compilation_time": 0.0,
            "injection_time": 0.0,
        }

    def compile_injector(self, plan: InjectionPlan) -> Callable:
        """编译注入器函数"""
        if plan.bean_name in self._compiled_injectors:
            return self._compiled_injectors[plan.bean_name]

        start_time = time.time()

        # 生成完整的注入函数代码
        injector_code = self._generate_function(plan)

        # 编译注入函数
        compiled_func = self._compile_function(injector_code, plan)

        # 缓存编译结果
        self._compiled_injectors[plan.bean_name] = compiled_func

        # 统计编译时间
        self._injection_stats["compilation_time"] += time.time() - start_time

        return compiled_func

    def _generate_function(self, plan: InjectionPlan) -> str:
        """生成注入器函数代码"""
        func_name = f"inject_{plan.bean_name.replace('-', '_')}"

        code_lines = [
            f"def {func_name}(factory):",
            "    # 零反射高性能注入",
        ]

        # 添加构造函数注入代码
        if plan.constructor_injection_code:
            code_lines.append(f"    {plan.constructor_injection_code}")
        else:
            code_lines.append(f"    instance = {plan.bean_class.__name__}()")

        # 添加字段注入代码
        if plan.field_injection_code:
            for line in plan.field_injection_code.split("\n"):
                code_lines.append(f"    {line}")

        # 添加方法注入代码
        if plan.method_injection_code:
            for line in plan.method_injection_code.split("\n"):
                code_lines.append(f"    {line}")

        code_lines.append("    return instance")

        return "\n".join(code_lines)

    def _compile_function(self, code: str, plan: InjectionPlan) -> Callable:
        """编译函数代码"""
        try:
            # 创建执行环境
            namespace = {
                plan.bean_class.__name__: plan.bean_class,
            }

            # 编译并执行代码
            exec(code, namespace)

            # 获取编译后的函数
            func_name = f"inject_{plan.bean_name.replace('-', '_')}"
            return namespace[func_name]

        except Exception:
            # 编译失败，返回默认注入器
            return lambda _: plan.bean_class()

    def inject_compiled(self, bean_name: str, factory) -> Any:
        """使用编译后的代码进行注入"""
        start_time = time.time()

        if bean_name in self._compiled_injectors:
            injector_func = self._compiled_injectors[bean_name]
            instance = injector_func(factory)

            # 统计注入时间
            self._injection_stats["injection_time"] += time.time() - start_time
            self._injection_stats["zero_reflection_injections"] += 1

            return instance

        return None

    def get_stats(self) -> dict[str, Any]:
        """获取注入统计信息"""
        return self._injection_stats.copy()


class AsyncDependencyInjector:
    """异步依赖注入器 - 支持异步Bean创建和注入"""

    def __init__(self):
        self._async_injection_stats = {
            "async_injections": 0,
            "concurrent_injections": 0,
            "async_injection_time": 0.0,
        }

    async def inject_async(self, bean: Any, bean_definition: BeanDefinition, factory) -> None:
        """异步依赖注入"""
        start_time = time.time()

        try:
            # 并发注入多个依赖
            injection_tasks = []

            # 异步字段注入
            if hasattr(bean_definition, "field_dependencies"):
                for field_name, dep_name in bean_definition.field_dependencies.items():
                    task = self._inject_field_async(bean, field_name, dep_name, factory)
                    injection_tasks.append(task)

            # 等待所有注入完成
            if injection_tasks:
                await asyncio.gather(*injection_tasks)
                self._async_injection_stats["concurrent_injections"] += len(injection_tasks)

            self._async_injection_stats["async_injections"] += 1
            self._async_injection_stats["async_injection_time"] += time.time() - start_time

        except Exception as e:
            raise RuntimeError(f"Async injection failed for bean '{bean_definition.bean_name}': {e}") from e

    async def _inject_field_async(self, bean: Any, field_name: str, dep_name: str, factory) -> None:
        """异步字段注入"""
        try:
            # 异步获取依赖Bean
            dependency = await self._get_bean_async(dep_name, factory)
            setattr(bean, field_name, dependency)
        except Exception as e:
            raise RuntimeError(f"Failed to inject field '{field_name}': {e}") from e

    async def _get_bean_async(self, bean_name: str, factory) -> Any:
        """异步获取Bean"""
        # 如果Bean工厂支持异步，使用异步方法
        if hasattr(factory, "get_bean_async"):
            return await factory.get_bean_async(bean_name)
        else:
            # 否则在线程池中执行同步方法
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, factory.get_bean, bean_name)

    def get_async_stats(self) -> dict[str, Any]:
        """获取异步注入统计信息"""
        return self._async_injection_stats.copy()


class HighPerformanceDependencyInjector:
    """高性能依赖注入器 - 整合编译时优化、零反射和异步注入"""

    def __init__(self, enable_compilation: bool = True, enable_async: bool = True):
        # 配置
        self.enable_compilation = enable_compilation
        self.enable_async = enable_async

        # 延迟初始化的组件
        self._analyzer: Optional[CompileTimeAnalyzer] = None
        self._zero_reflection_injector: Optional[ZeroReflectionInjector] = None
        self._async_injector: Optional[AsyncDependencyInjector] = None

        # 回退到传统注入器
        self._fallback_injector: Optional[DependencyInjector] = None

        # 性能统计
        self._performance_stats = {
            "total_injections": 0,
            "compiled_injections": 0,
            "async_injections": 0,
            "fallback_injections": 0,
            "average_injection_time": 0.0,
            "compilation_cache_hits": 0,
        }

        # 线程安全
        self._lock = threading.RLock()

        # 预编译缓存
        self._precompiled_plans: dict[str, InjectionPlan] = {}

    def _get_analyzer(self) -> CompileTimeAnalyzer:
        """获取编译时分析器（延迟初始化）"""
        if self._analyzer is None:
            self._analyzer = CompileTimeAnalyzer()
        return self._analyzer

    def _get_zero_reflection_injector(self) -> ZeroReflectionInjector:
        """获取零反射注入器（延迟初始化）"""
        if self._zero_reflection_injector is None:
            self._zero_reflection_injector = ZeroReflectionInjector(self._get_analyzer())
        return self._zero_reflection_injector

    def _get_async_injector(self) -> AsyncDependencyInjector:
        """获取异步注入器（延迟初始化）"""
        if self._async_injector is None:
            self._async_injector = AsyncDependencyInjector()
        return self._async_injector

    def set_fallback_injector(self, injector: "DependencyInjector") -> None:
        """设置回退注入器"""
        self._fallback_injector = injector

    def inject(self, bean: Any, bean_definition: BeanDefinition) -> None:
        """高性能依赖注入 - 自动选择最优注入策略"""
        start_time = time.time()

        with self._lock:
            try:
                # 尝试使用编译时优化注入
                if self.enable_compilation and self._try_compiled_injection(bean, bean_definition):
                    self._performance_stats["compiled_injections"] += 1
                    return

                # 回退到传统注入器
                if self._fallback_injector:
                    self._fallback_injector.inject(bean, bean_definition)
                    self._performance_stats["fallback_injections"] += 1
                else:
                    # 使用基础注入逻辑
                    self._basic_injection(bean, bean_definition)

            finally:
                # 更新性能统计
                injection_time = time.time() - start_time
                self._update_performance_stats(injection_time)

    async def inject_async(self, bean: Any, bean_definition: BeanDefinition, factory) -> None:
        """异步依赖注入"""
        if self.enable_async:
            async_injector = self._get_async_injector()
            await async_injector.inject_async(bean, bean_definition, factory)
            self._performance_stats["async_injections"] += 1
        else:
            # 在线程池中执行同步注入
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, self.inject, bean, bean_definition)

    def resolve_constructor(self, bean_definition: BeanDefinition) -> list[Any]:
        """高性能构造函数依赖解析"""
        # 尝试使用预编译的注入计划
        if self.enable_compilation:
            plan = self._get_or_create_injection_plan(bean_definition)
            if plan and plan.constructor_dependencies:
                return self._resolve_compiled_constructor(plan, bean_definition)

        # 回退到传统解析
        if self._fallback_injector:
            return self._fallback_injector.resolve_constructor(bean_definition)

        return []

    def precompile_bean(self, bean_definition: BeanDefinition) -> None:
        """预编译Bean的注入计划"""
        if not self.enable_compilation:
            return

        try:
            analyzer = self._get_analyzer()
            zero_reflection_injector = self._get_zero_reflection_injector()
            plan = analyzer.analyze_bean_class(bean_definition.bean_class, bean_definition.bean_name)
            zero_reflection_injector.compile_injector(plan)
            self._precompiled_plans[bean_definition.bean_name] = plan
        except Exception:
            # 预编译失败，记录但不影响运行
            pass

    def _try_compiled_injection(self, bean: Any, bean_definition: BeanDefinition) -> bool:
        """尝试使用编译时优化注入"""
        try:
            # 检查是否有预编译的注入计划
            plan = self._precompiled_plans.get(bean_definition.bean_name)
            if not plan:
                # 动态分析和编译
                analyzer = self._get_analyzer()
                zero_reflection_injector = self._get_zero_reflection_injector()
                plan = analyzer.analyze_bean_class(bean_definition.bean_class, bean_definition.bean_name)
                zero_reflection_injector.compile_injector(plan)
                self._precompiled_plans[bean_definition.bean_name] = plan

            # 使用零反射注入器
            if hasattr(bean_definition, "bean_factory"):
                factory = bean_definition.bean_factory
                zero_reflection_injector = self._get_zero_reflection_injector()
                compiled_bean = zero_reflection_injector.inject_compiled(bean_definition.bean_name, factory)

                if compiled_bean:
                    # 将编译注入的结果复制到目标Bean
                    self._copy_bean_state(compiled_bean, bean)
                    return True

            return False

        except Exception:
            # 编译注入失败，回退到传统方式
            return False

    def _get_or_create_injection_plan(self, bean_definition: BeanDefinition) -> Optional[InjectionPlan]:
        """获取或创建注入计划"""
        plan = self._precompiled_plans.get(bean_definition.bean_name)
        if not plan:
            try:
                analyzer = self._get_analyzer()
                plan = analyzer.analyze_bean_class(bean_definition.bean_class, bean_definition.bean_name)
                self._precompiled_plans[bean_definition.bean_name] = plan
            except Exception:
                return None
        return plan

    def _resolve_compiled_constructor(self, plan: InjectionPlan, bean_definition: BeanDefinition) -> list[Any]:
        """使用编译计划解析构造函数依赖"""
        args = []
        if hasattr(bean_definition, "bean_factory"):
            factory = bean_definition.bean_factory
            for dep_name in plan.constructor_dependencies:
                dependency = factory.get_bean(dep_name)
                args.append(dependency)
        return args

    def _basic_injection(self, bean: Any, bean_definition: BeanDefinition) -> None:
        """基础注入逻辑（当没有回退注入器时使用）"""
        # 简化的注入逻辑，暂时不做任何操作
        # 实际实现中可以添加基础的依赖注入逻辑
        _ = bean, bean_definition  # 避免未使用参数警告

    def _copy_bean_state(self, source: Any, target: Any) -> None:
        """复制Bean状态"""
        # 复制所有非私有属性
        for attr_name in dir(source):
            if not attr_name.startswith("_") and hasattr(source, attr_name):
                try:
                    attr_value = getattr(source, attr_name)
                    if not callable(attr_value):
                        setattr(target, attr_name, attr_value)
                except Exception:
                    continue

    def _update_performance_stats(self, injection_time: float) -> None:
        """更新性能统计"""
        self._performance_stats["total_injections"] += 1

        # 计算平均注入时间
        total = self._performance_stats["total_injections"]
        current_avg = self._performance_stats["average_injection_time"]
        self._performance_stats["average_injection_time"] = (current_avg * (total - 1) + injection_time) / total

    def get_performance_stats(self) -> dict[str, Any]:
        """获取性能统计信息"""
        stats = self._performance_stats.copy()

        # 添加子组件统计（如果已初始化）
        if self._zero_reflection_injector is not None:
            stats.update(self._zero_reflection_injector.get_stats())
        if self._async_injector is not None:
            stats.update(self._async_injector.get_async_stats())

        # 计算性能指标
        total = stats["total_injections"]
        if total > 0:
            stats["compiled_injection_ratio"] = stats["compiled_injections"] / total
            stats["async_injection_ratio"] = stats["async_injections"] / total
            stats["fallback_injection_ratio"] = stats["fallback_injections"] / total

        return stats

    def clear_cache(self) -> None:
        """清理缓存"""
        with self._lock:
            self._precompiled_plans.clear()
            if self._zero_reflection_injector is not None:
                self._zero_reflection_injector._compiled_injectors.clear()

    def get_cache_info(self) -> dict[str, Any]:
        """获取缓存信息"""
        info = {
            "precompiled_plans": len(self._precompiled_plans),
            "compiled_injectors": 0,
            "analyzer_cache": 0,
        }

        if self._zero_reflection_injector is not None:
            info["compiled_injectors"] = len(self._zero_reflection_injector._compiled_injectors)

        if self._analyzer is not None:
            info["analyzer_cache"] = len(self._analyzer._analyzed_classes)

        return info


class HighPerformanceInjectorManager:
    """高性能注入器管理器 - 统一管理和协调高性能注入器"""

    def __init__(self):
        # 高性能注入器（延迟初始化）
        self._hp_injector: Optional[HighPerformanceDependencyInjector] = None

        # Bean工厂引用
        self._bean_factory: Optional[Any] = None

        # 配置
        self._config = {
            "enable_compilation": True,
            "enable_async": True,
            "enable_precompilation": True,
            "compilation_threshold": 10,  # 使用次数超过此值时进行预编译
        }

        # 统计信息
        self._usage_stats: dict[str, int] = {}
        self._performance_metrics = {
            "total_injections": 0,
            "hp_injections": 0,
            "traditional_injections": 0,
            "precompiled_beans": 0,
            "average_injection_time": 0.0,
        }

        # 线程安全
        self._lock = threading.RLock()

    def _get_hp_injector(self) -> HighPerformanceDependencyInjector:
        """获取高性能注入器（延迟初始化）"""
        if self._hp_injector is None:
            self._hp_injector = HighPerformanceDependencyInjector()
        return self._hp_injector

    def set_bean_factory(self, bean_factory: Any) -> None:
        """设置Bean工厂"""
        self._bean_factory = bean_factory

    def inject_dependencies(self, bean: Any, bean_definition: BeanDefinition) -> None:
        """注入依赖 - 智能选择注入策略"""
        start_time = time.time()

        with self._lock:
            try:
                # 创建适配器
                adapter = self._create_adapter(bean_definition)

                # 更新使用统计
                self._update_usage_stats(bean_definition.bean_name)

                # 检查是否需要预编译
                if self._should_precompile(bean_definition.bean_name):
                    hp_injector = self._get_hp_injector()
                    hp_injector.precompile_bean(adapter)
                    self._performance_metrics["precompiled_beans"] += 1

                # 执行注入
                hp_injector = self._get_hp_injector()
                hp_injector.inject(bean, adapter)
                self._performance_metrics["hp_injections"] += 1

            except Exception as e:
                # 记录错误但不中断
                self._performance_metrics["traditional_injections"] += 1
                raise e

            finally:
                # 更新性能指标
                injection_time = time.time() - start_time
                self._update_performance_metrics(injection_time)

    def _create_adapter(self, bean_definition: BeanDefinition) -> BeanDefinitionAdapter:
        """创建Bean定义适配器"""
        adapter = BeanDefinitionAdapter(bean_definition, self._bean_factory)
        return adapter

    def _update_usage_stats(self, bean_name: str) -> None:
        """更新使用统计"""
        if bean_name not in self._usage_stats:
            self._usage_stats[bean_name] = 0
        self._usage_stats[bean_name] += 1

    def _should_precompile(self, bean_name: str) -> bool:
        """检查是否应该预编译"""
        if not self._config["enable_precompilation"]:
            return False

        usage_count = self._usage_stats.get(bean_name, 0)
        return usage_count >= self._config["compilation_threshold"]

    def _update_performance_metrics(self, injection_time: float) -> None:
        """更新性能指标"""
        self._performance_metrics["total_injections"] += 1

        # 计算平均注入时间
        total = self._performance_metrics["total_injections"]
        current_avg = self._performance_metrics["average_injection_time"]
        new_avg = (current_avg * (total - 1) + injection_time) / total
        self._performance_metrics["average_injection_time"] = new_avg

    def get_performance_report(self) -> dict[str, Any]:
        """获取性能报告"""
        with self._lock:
            return {
                "metrics": self._performance_metrics.copy(),
                "usage_stats": self._usage_stats.copy(),
                "config": self._config.copy(),
                "hp_injector_info": self._get_hp_injector().get_cache_info() if self._hp_injector else {},
            }

    def configure(self, **config) -> None:
        """配置管理器"""
        with self._lock:
            self._config.update(config)

    def reset_stats(self) -> None:
        """重置统计信息"""
        with self._lock:
            self._usage_stats.clear()
            self._performance_metrics = {
                "total_injections": 0,
                "hp_injections": 0,
                "traditional_injections": 0,
                "precompiled_beans": 0,
                "average_injection_time": 0.0,
            }


# 便捷函数已删除 - 请直接使用类构造器
# 使用方式：
# manager = HighPerformanceInjectorManager()
# manager.configure(**config)
# report = manager.get_performance_report()
