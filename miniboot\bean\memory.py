#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Bean内存管理模块 - 提供内存优化、监控和分析的统一功能
"""

import contextlib
import gc
import threading
import time
import weakref
from collections import defaultdict, deque
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Optional

from .definition import BeanDefinition
from .scopes import BeanScope


class MemoryGeneration(Enum):
    """内存分代类型"""

    YOUNG = "young"  # 新生代 - 短期存活的Bean
    OLD = "old"  # 老年代 - 长期存活的Bean
    PERMANENT = "permanent"  # 永久代 - 单例Bean


@dataclass
class MemoryStats:
    """内存统计信息"""

    total_memory_mb: float = 0.0
    used_memory_mb: float = 0.0
    free_memory_mb: float = 0.0
    young_generation_count: int = 0
    old_generation_count: int = 0
    permanent_generation_count: int = 0
    gc_collections: int = 0
    weak_references_count: int = 0


@dataclass
class BeanMemoryInfo:
    """Bean内存信息"""

    bean_name: str
    bean_class: type
    scope: BeanScope
    generation: MemoryGeneration
    memory_size_bytes: int = 0
    creation_time: float = field(default_factory=time.time)
    last_access_time: float = field(default_factory=time.time)
    access_count: int = 0
    is_weak_reference: bool = False


@dataclass
class MemoryAlert:
    """内存警报"""

    alert_type: str
    severity: str  # LOW, MEDIUM, HIGH, CRITICAL
    message: str
    timestamp: float
    details: dict[str, Any] = field(default_factory=dict)


@dataclass
class MemoryTrend:
    """内存趋势数据"""

    timestamp: float
    memory_mb: float
    young_generation_count: int
    old_generation_count: int
    permanent_generation_count: int


class MemoryConfig:
    """内存优化配置"""

    def __init__(self):
        # 内存优化器配置
        self.enable_memory_optimization = True
        self.enable_gc_optimization = True
        self.young_generation_limit = 1000
        self.old_generation_limit = 5000

        # 内存监控配置
        self.enable_memory_monitoring = True
        self.alert_threshold_mb = 100.0
        self.trend_history_size = 1000
        self.monitoring_interval = 30.0

        # 内存分析配置
        self.enable_memory_analysis = True
        self.leak_detection_threshold = 0.8
        self.analysis_cache_timeout = 300


class MemoryOptimizer:
    """Bean内存优化器

    提供内存管理、弱引用管理和垃圾回收优化功能。
    """

    def __init__(self, config: Optional[MemoryConfig] = None):
        """初始化内存优化器

        Args:
            config: 内存优化配置
        """
        self.config = config or MemoryConfig()
        self._lock = threading.RLock()

        # 分代内存管理
        self._young_generation: weakref.WeakSet = weakref.WeakSet()  # 新生代
        self._old_generation: weakref.WeakSet = weakref.WeakSet()  # 老年代
        self._permanent_generation: dict[str, Any] = {}  # 永久代(强引用)

        # 弱引用管理
        self._weak_references: dict[str, weakref.ref] = {}

        # Bean内存信息
        self._bean_memory_info: dict[str, BeanMemoryInfo] = {}

        # 配置
        self._young_generation_limit = self.config.young_generation_limit
        self._old_generation_limit = self.config.old_generation_limit

        # 统计信息
        self._stats = MemoryStats()

    def register_bean(self, bean_name: str, bean: Any, bean_definition: BeanDefinition) -> None:
        """注册Bean到内存管理器

        Args:
            bean_name: Bean名称
            bean: Bean实例
            bean_definition: Bean定义
        """
        with self._lock:
            # 确定内存分代
            generation = self._determine_generation(bean_definition)

            # 创建内存信息
            memory_info = BeanMemoryInfo(
                bean_name=bean_name,
                bean_class=bean_definition.bean_class,
                scope=bean_definition.scope,
                generation=generation,
                memory_size_bytes=self._estimate_memory_size(bean),
            )

            self._bean_memory_info[bean_name] = memory_info

            # 根据分代策略管理Bean
            if generation == MemoryGeneration.YOUNG:
                self._young_generation.add(bean)
            elif generation == MemoryGeneration.OLD:
                self._old_generation.add(bean)
            elif generation == MemoryGeneration.PERMANENT:
                self._permanent_generation[bean_name] = bean

            # 创建弱引用（除了永久代）
            if generation != MemoryGeneration.PERMANENT:
                self._weak_references[bean_name] = weakref.ref(bean, self._cleanup_callback(bean_name))
                memory_info.is_weak_reference = True

            # 检查是否需要清理
            self._check_generation_limits()

    def _determine_generation(self, bean_definition: BeanDefinition) -> MemoryGeneration:
        """确定Bean的内存分代

        Args:
            bean_definition: Bean定义

        Returns:
            MemoryGeneration: 内存分代类型
        """
        if bean_definition.scope == BeanScope.SINGLETON:
            return MemoryGeneration.PERMANENT
        elif bean_definition.scope == BeanScope.PROTOTYPE:
            return MemoryGeneration.YOUNG
        else:
            return MemoryGeneration.OLD

    def _estimate_memory_size(self, obj: Any) -> int:
        """估算对象内存大小

        Args:
            obj: 要估算的对象

        Returns:
            int: 估算的内存大小（字节）
        """
        try:
            import sys

            return sys.getsizeof(obj)
        except Exception:
            return 0

    def _cleanup_callback(self, bean_name: str):
        """弱引用清理回调

        Args:
            bean_name: Bean名称

        Returns:
            callable: 清理回调函数
        """

        def cleanup(ref):
            with contextlib.suppress(Exception):
                self._weak_references.pop(bean_name, None)
                self._bean_memory_info.pop(bean_name, None)

        return cleanup

    def _check_generation_limits(self) -> None:
        """检查分代限制并执行清理"""
        # 检查新生代限制
        if len(self._young_generation) > self._young_generation_limit:
            self._promote_young_to_old()

        # 检查老年代限制
        if len(self._old_generation) > self._old_generation_limit:
            self._cleanup_old_generation()

    def _promote_young_to_old(self) -> None:
        """将新生代Bean提升到老年代"""
        # 简化实现：清理一部分新生代Bean
        young_beans = list(self._young_generation)
        cleanup_count = len(young_beans) // 2

        for i in range(cleanup_count):
            if i < len(young_beans):
                bean = young_beans[i]
                with contextlib.suppress(Exception):
                    self._young_generation.discard(bean)
                    self._old_generation.add(bean)

    def _cleanup_old_generation(self) -> None:
        """清理老年代Bean"""
        # 简化实现：清理一部分老年代Bean
        old_beans = list(self._old_generation)
        cleanup_count = len(old_beans) // 4

        for i in range(cleanup_count):
            if i < len(old_beans):
                bean = old_beans[i]
                with contextlib.suppress(Exception):
                    self._old_generation.discard(bean)

    def cleanup_expired_beans(self) -> int:
        """清理过期的Bean

        Returns:
            int: 清理的Bean数量
        """
        with self._lock:
            cleaned_count = 0

            # 清理无效的弱引用
            invalid_refs = []
            for bean_name, ref in self._weak_references.items():
                if ref() is None:
                    invalid_refs.append(bean_name)

            for bean_name in invalid_refs:
                self._weak_references.pop(bean_name, None)
                self._bean_memory_info.pop(bean_name, None)
                cleaned_count += 1

            return cleaned_count

    def optimize_memory(self) -> dict[str, Any]:
        """执行内存优化，返回优化结果 - 核心优化实现"""
        with self._lock:
            start_time = time.time()

            # 清理过期Bean
            cleaned_beans = self.cleanup_expired_beans()

            # 执行垃圾回收
            gc_collected = 0
            if self.config.enable_gc_optimization:
                gc_collected = gc.collect()

            # 更新统计信息
            self._update_stats()

            optimization_time = time.time() - start_time

            return {
                "optimization_time": optimization_time,
                "cleaned_beans": cleaned_beans,
                "gc_collected": gc_collected,
                "memory_stats": self._stats,
                "timestamp": time.time(),
            }

    def _update_stats(self) -> None:
        """更新内存统计信息"""
        self._stats.young_generation_count = len(self._young_generation)
        self._stats.old_generation_count = len(self._old_generation)
        self._stats.permanent_generation_count = len(self._permanent_generation)
        self._stats.weak_references_count = len(self._weak_references)

        # 获取系统内存信息
        try:
            import psutil

            memory = psutil.virtual_memory()
            self._stats.total_memory_mb = memory.total / (1024 * 1024)
            self._stats.used_memory_mb = memory.used / (1024 * 1024)
            self._stats.free_memory_mb = memory.available / (1024 * 1024)
        except ImportError:
            # 如果没有psutil，使用默认值
            pass

    def get_memory_info(self) -> dict[str, BeanMemoryInfo]:
        """获取所有Bean的内存信息

        Returns:
            dict[str, BeanMemoryInfo]: Bean内存信息字典
        """
        with self._lock:
            return self._bean_memory_info.copy()

    def get_memory_stats(self) -> MemoryStats:
        """获取内存统计信息

        Returns:
            MemoryStats: 内存统计信息
        """
        with self._lock:
            self._update_stats()
            return self._stats


class MemoryMonitor:
    """Bean内存监控器

    提供内存使用监控、内存泄漏检测和性能分析功能。
    """

    def __init__(self, config: Optional[MemoryConfig] = None):
        """初始化内存监控器

        Args:
            config: 内存监控配置
        """
        self.config = config or MemoryConfig()
        self._lock = threading.RLock()

        # 监控配置
        self.alert_threshold_mb = self.config.alert_threshold_mb
        self.monitoring_interval = self.config.monitoring_interval

        # 监控数据
        self._memory_trends: deque = deque(maxlen=self.config.trend_history_size)
        self._alerts: list[MemoryAlert] = []
        self._bean_memory_usage: dict[str, list[float]] = defaultdict(list)

        # 统计数据
        self._peak_memory_usage = 0.0
        self._monitoring_active = False
        self._monitoring_thread: Optional[threading.Thread] = None

    def start_monitoring(self) -> None:
        """启动内存监控"""
        with self._lock:
            if not self._monitoring_active:
                self._monitoring_active = True
                self._monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
                self._monitoring_thread.start()

    def stop_monitoring(self) -> None:
        """停止内存监控"""
        with self._lock:
            self._monitoring_active = False
            if self._monitoring_thread:
                self._monitoring_thread.join(timeout=1.0)

    def record_stats(self, memory_mb: float, stats: MemoryStats) -> None:
        """记录内存统计信息

        Args:
            memory_mb: 内存使用量(MB)
            stats: 内存统计信息
        """
        with self._lock:
            # 记录内存趋势
            trend = MemoryTrend(
                timestamp=time.time(),
                memory_mb=memory_mb,
                young_generation_count=stats.young_generation_count,
                old_generation_count=stats.old_generation_count,
                permanent_generation_count=stats.permanent_generation_count,
            )

            self._memory_trends.append(trend)

            # 更新峰值内存使用
            if memory_mb > self._peak_memory_usage:
                self._peak_memory_usage = memory_mb

            # 检查内存警报
            self._check_memory_alerts(memory_mb, stats)

    def record_usage(self, bean_name: str, memory_mb: float) -> None:
        """记录Bean内存使用情况

        Args:
            bean_name: Bean名称
            memory_mb: 内存使用量(MB)
        """
        with self._lock:
            self._bean_memory_usage[bean_name].append(memory_mb)
            # 保持最近100条记录
            if len(self._bean_memory_usage[bean_name]) > 100:
                self._bean_memory_usage[bean_name] = self._bean_memory_usage[bean_name][-100:]

    def get_trends(self, hours: int = 1) -> list[MemoryTrend]:
        """获取指定时间范围内的内存趋势"""
        with self._lock:
            cutoff_time = time.time() - (hours * 3600)
            return [trend for trend in self._memory_trends if trend.timestamp >= cutoff_time]

    def get_alerts(self, severity: Optional[str] = None, hours: int = 24) -> list[MemoryAlert]:
        """获取内存警报"""
        with self._lock:
            cutoff_time = time.time() - (hours * 3600)
            alerts = [alert for alert in self._alerts if alert.timestamp >= cutoff_time]

            if severity:
                alerts = [alert for alert in alerts if alert.severity == severity]

            return sorted(alerts, key=lambda x: x.timestamp, reverse=True)

    def get_summary(self) -> dict[str, Any]:
        """获取内存使用摘要"""
        with self._lock:
            current_time = time.time()
            recent_trends = [trend for trend in self._memory_trends if current_time - trend.timestamp < 3600]

            if not recent_trends:
                return {}

            current_memory = recent_trends[-1].memory_mb if recent_trends else 0.0
            avg_memory = sum(trend.memory_mb for trend in recent_trends) / len(recent_trends)

            return {
                "current_memory_mb": current_memory,
                "average_memory_mb": avg_memory,
                "peak_memory_mb": self._peak_memory_usage,
                "total_alerts": len(self._alerts),
                "recent_alerts": len([alert for alert in self._alerts if current_time - alert.timestamp < 3600]),
                "monitoring_active": self._monitoring_active,
                "trend_data_points": len(self._memory_trends),
                "monitored_beans": len(self._bean_memory_usage),
            }

    def analyze_memory_leaks(self) -> list[dict[str, Any]]:
        """分析潜在的内存泄漏"""
        with self._lock:
            leak_candidates = []

            for bean_name, usage_history in self._bean_memory_usage.items():
                if len(usage_history) < 10:  # 需要足够的数据点
                    continue

                # 计算内存使用趋势
                recent_usage = usage_history[-10:]
                early_usage = usage_history[:10] if len(usage_history) >= 20 else usage_history[: len(usage_history) // 2]

                if not early_usage:
                    continue

                recent_avg = sum(recent_usage) / len(recent_usage)
                early_avg = sum(early_usage) / len(early_usage)

                # 检查是否有显著增长
                if recent_avg > early_avg * 1.5:  # 增长超过50%
                    growth_rate = (recent_avg - early_avg) / early_avg
                    leak_candidates.append(
                        {
                            "bean_name": bean_name,
                            "growth_rate": growth_rate,
                            "early_avg_mb": early_avg,
                            "recent_avg_mb": recent_avg,
                            "data_points": len(usage_history),
                            "severity": "HIGH" if growth_rate > 1.0 else "MEDIUM",
                        }
                    )

            return leak_candidates

    def get_optimization_suggestions(self) -> list[str]:
        """获取内存优化建议"""
        suggestions = []

        # 分析内存使用情况
        summary = self.get_summary()
        if not summary:
            return suggestions

        current_memory = summary.get("current_memory_mb", 0)
        peak_memory = summary.get("peak_memory_mb", 0)

        # 基于内存使用情况提供建议
        if current_memory > self.alert_threshold_mb:
            suggestions.append("当前内存使用过高，建议执行垃圾回收")

        if peak_memory > current_memory * 2:
            suggestions.append("内存使用波动较大，建议优化Bean创建策略")

        # 分析内存泄漏
        leak_candidates = self.analyze_memory_leaks()
        if leak_candidates:
            high_risk_beans = [bean for bean in leak_candidates if bean["severity"] == "HIGH"]
            if high_risk_beans:
                bean_names = [bean["bean_name"] for bean in high_risk_beans[:3]]
                suggestions.append(f"发现潜在内存泄漏Bean: {', '.join(bean_names)}")

        # 分析警报
        recent_alerts = self.get_alerts(hours=1)
        if len(recent_alerts) > 5:
            suggestions.append("最近警报频繁，建议检查内存配置")

        return suggestions

    def _monitoring_loop(self) -> None:
        """监控循环"""
        while self._monitoring_active:
            with contextlib.suppress(Exception):
                # 这里可以集成具体的内存统计逻辑
                # 目前作为框架，等待外部调用record_memory_stats
                time.sleep(self.monitoring_interval)

    def _check_memory_alerts(self, memory_mb: float, stats: MemoryStats) -> None:
        """检查内存警报条件"""
        current_time = time.time()

        # 内存使用过高警报
        if memory_mb > self.alert_threshold_mb:
            alert = MemoryAlert(
                alert_type="HIGH_MEMORY_USAGE",
                severity="HIGH",
                message=f"内存使用过高: {memory_mb:.2f}MB (阈值: {self.alert_threshold_mb}MB)",
                timestamp=current_time,
                details={"memory_mb": memory_mb, "threshold_mb": self.alert_threshold_mb},
            )
            self._alerts.append(alert)

        # 内存增长过快警报
        if len(self._memory_trends) >= 2:
            prev_trend = self._memory_trends[-2]
            growth_rate = (memory_mb - prev_trend.memory_mb) / prev_trend.memory_mb if prev_trend.memory_mb > 0 else 0

            if growth_rate > 0.1:  # 增长超过10%
                alert = MemoryAlert(
                    alert_type="RAPID_MEMORY_GROWTH",
                    severity="MEDIUM",
                    message=f"内存增长过快: {growth_rate:.1%}",
                    timestamp=current_time,
                    details={"growth_rate": growth_rate, "current_mb": memory_mb, "previous_mb": prev_trend.memory_mb},
                )
                self._alerts.append(alert)


class MemoryAnalyzer:
    """内存分析器

    提供高级内存分析功能，包括内存分布分析、性能报告生成等。
    """

    def __init__(self):
        self._analysis_cache: dict[str, Any] = {}
        self._cache_timeout = 300  # 5分钟缓存

    def analyze_bean_memory_distribution(self, memory_info_dict: dict[str, BeanMemoryInfo]) -> dict[str, Any]:
        """分析Bean内存分布"""
        if not memory_info_dict:
            return {}

        # 按作用域分组
        scope_distribution = defaultdict(list)
        generation_distribution = defaultdict(list)

        for _bean_name, info in memory_info_dict.items():
            scope_distribution[info.scope.value].append(info)
            generation_distribution[info.generation.value].append(info)

        # 计算统计信息
        total_memory = sum(info.memory_size_bytes for info in memory_info_dict.values())
        bean_count = len(memory_info_dict)

        # 作用域分析
        scope_stats = {}
        for scope, beans in scope_distribution.items():
            scope_memory = sum(bean.memory_size_bytes for bean in beans)
            scope_stats[scope] = {
                "bean_count": len(beans),
                "total_memory_bytes": scope_memory,
                "percentage": (scope_memory / total_memory * 100) if total_memory > 0 else 0,
                "avg_memory_per_bean": scope_memory / len(beans) if beans else 0,
            }

        # 分代分析
        generation_stats = {}
        for generation, beans in generation_distribution.items():
            generation_memory = sum(bean.memory_size_bytes for bean in beans)
            generation_stats[generation] = {
                "bean_count": len(beans),
                "total_memory_bytes": generation_memory,
                "percentage": (generation_memory / total_memory * 100) if total_memory > 0 else 0,
                "avg_memory_per_bean": generation_memory / len(beans) if beans else 0,
            }

        # 内存使用排行
        top_memory_beans = sorted(memory_info_dict.values(), key=lambda x: x.memory_size_bytes, reverse=True)[:10]

        analysis = {
            "total_beans": bean_count,
            "total_memory_bytes": total_memory,
            "scope_distribution": scope_stats,
            "generation_distribution": generation_stats,
            "top_memory_consumers": [
                {
                    "bean_name": bean.bean_name,
                    "memory_bytes": bean.memory_size_bytes,
                    "scope": bean.scope.value,
                    "generation": bean.generation.value,
                }
                for bean in top_memory_beans
            ],
            "analysis_timestamp": time.time(),
        }

        return analysis

    def generate_memory_report(self, memory_summary: dict[str, Any], trends: list[MemoryTrend], alerts: list[MemoryAlert]) -> str:
        """生成内存分析报告"""
        report_lines = []

        # 报告头部
        report_lines.append("=" * 60)
        report_lines.append("Bean内存使用分析报告")
        report_lines.append("=" * 60)
        report_lines.append(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")

        # 内存摘要
        if memory_summary:
            report_lines.append("📊 内存使用摘要")
            report_lines.append("-" * 30)
            report_lines.append(f"当前内存使用: {memory_summary.get('current_memory_mb', 0):.2f} MB")
            report_lines.append(f"平均内存使用: {memory_summary.get('average_memory_mb', 0):.2f} MB")
            report_lines.append(f"峰值内存使用: {memory_summary.get('peak_memory_mb', 0):.2f} MB")
            report_lines.append(f"监控状态: {'活跃' if memory_summary.get('monitoring_active') else '非活跃'}")
            report_lines.append("")

        # 内存趋势
        if trends:
            report_lines.append("📈 内存使用趋势")
            report_lines.append("-" * 30)
            report_lines.append(f"趋势数据点: {len(trends)}")
            if len(trends) >= 2:
                first_trend = trends[0]
                last_trend = trends[-1]
                memory_change = last_trend.memory_mb - first_trend.memory_mb
                report_lines.append(f"内存变化: {memory_change:+.2f} MB")
            report_lines.append("")

        # 警报信息
        if alerts:
            report_lines.append("⚠️ 内存警报")
            report_lines.append("-" * 30)
            report_lines.append(f"总警报数: {len(alerts)}")

            # 按严重程度分组
            severity_counts = defaultdict(int)
            for alert in alerts:
                severity_counts[alert.severity] += 1

            for severity, count in severity_counts.items():
                report_lines.append(f"{severity}: {count}")

            # 显示最近的警报
            recent_alerts = sorted(alerts, key=lambda x: x.timestamp, reverse=True)[:5]
            report_lines.append("\n最近警报:")
            for alert in recent_alerts:
                timestamp = time.strftime("%H:%M:%S", time.localtime(alert.timestamp))
                report_lines.append(f"  [{timestamp}] {alert.severity}: {alert.message}")

        report_lines.append("")
        report_lines.append("=" * 60)

        return "\n".join(report_lines)


class MemoryOptimizedBeanFactory(BeanFactory):
    """内存优化的Bean工厂

    集成内存优化、监控和分析功能的Bean工厂。
    """

    def __init__(self, config: Optional[MemoryConfig] = None):
        """初始化内存优化Bean工厂

        Args:
            config: 内存配置
        """
        super().__init__()
        self.config = config or MemoryConfig()

        # 内存管理组件
        self._memory_optimizer: Optional[MemoryOptimizer] = None
        self._memory_monitor: Optional[MemoryMonitor] = None
        self._memory_analyzer: Optional[MemoryAnalyzer] = None

        # 初始化内存管理组件
        if self.config.enable_memory_optimization:
            self._memory_optimizer = MemoryOptimizer(self.config)

        if self.config.enable_memory_monitoring:
            self._memory_monitor = MemoryMonitor(self.config)
            self._memory_monitor.start_monitoring()

        if self.config.enable_memory_analysis:
            self._memory_analyzer = MemoryAnalyzer()

    def create_bean(self, bean_definition: BeanDefinition, *args, **kwargs) -> Any:
        """创建Bean实例（带内存优化）"""
        # 调用父类方法创建Bean
        bean = super().create_bean(bean_definition, *args, **kwargs)

        # 注册到内存优化器
        if self._memory_optimizer:
            self._memory_optimizer.register_bean(bean_definition.bean_name, bean, bean_definition)

        # 记录内存使用
        if self._memory_monitor:
            memory_size = self._estimate_bean_memory_size(bean)
            self._memory_monitor.record_usage(bean_definition.bean_name, memory_size / (1024 * 1024))

        return bean

    def _estimate_bean_memory_size(self, bean: Any) -> int:
        """估算Bean内存大小"""
        try:
            import sys

            return sys.getsizeof(bean)
        except Exception:
            return 0

    def get_memory_stats(self) -> dict[str, Any]:
        """获取内存统计信息"""
        if not self._memory_optimizer:
            return {}

        return {
            "optimizer_stats": self._memory_optimizer.get_memory_stats(),
            "memory_info": self._memory_optimizer.get_memory_info(),
        }

    def get_summary(self) -> dict[str, Any]:
        """获取内存使用摘要"""
        result = {}

        if self._memory_monitor:
            result.update(self._memory_monitor.get_summary())

        if self._memory_optimizer:
            result["optimizer_stats"] = self._memory_optimizer.get_memory_stats()

        return result

    def optimize_memory(self) -> dict[str, Any]:
        """手动执行内存优化 - 工厂级别的统一入口"""
        if not self._memory_optimizer:
            return {"error": "内存优化器未启用"}

        optimization_result = self._memory_optimizer.optimize_memory()

        # 记录优化后的内存状态
        if self._memory_monitor:
            stats = self._memory_optimizer.get_memory_stats()
            self._memory_monitor.record_stats(stats.used_memory_mb, stats)

        return optimization_result

    def get_memory_alerts(self, severity: Optional[str] = None, hours: int = 24) -> list[dict[str, Any]]:
        """获取内存警报"""
        if not self._memory_monitor:
            return []

        alerts = self._memory_monitor.get_alerts(severity, hours)
        return [
            {
                "type": alert.alert_type,
                "severity": alert.severity,
                "message": alert.message,
                "timestamp": alert.timestamp,
                "details": alert.details,
            }
            for alert in alerts
        ]

    def analyze_leaks(self) -> list[dict[str, Any]]:
        """分析内存泄漏"""
        if not self._memory_monitor:
            return []

        return self._memory_monitor.analyze_memory_leaks()

    def get_optimization_suggestions(self) -> list[str]:
        """获取内存优化建议"""
        suggestions = []

        if self._memory_monitor:
            suggestions.extend(self._memory_monitor.get_optimization_suggestions())

        if self._memory_optimizer:
            memory_info = self._memory_optimizer.get_memory_info()
            if len(memory_info) > 1000:
                suggestions.append("Bean数量过多，建议检查Bean作用域配置")

        return suggestions


# 便捷函数已删除 - 请直接使用类方法
# 使用方式：factory.optimize_memory()


# 所有便捷函数已删除 - 请直接使用类方法
