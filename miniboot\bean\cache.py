#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 多级缓存管理器 - 自适应缓存策略实现
"""

import asyncio
import threading
import time
import weakref
from collections import OrderedDict
from enum import Enum
from typing import Any, Optional

from loguru import logger


class CacheLevel(Enum):
    """缓存级别枚举"""

    L1_HOT = "L1_HOT"  # L1热点缓存 - 最频繁访问的Bean
    L2_WARM = "L2_WARM"  # L2温缓存 - 常用Bean
    L3_COLD = "L3_COLD"  # L3冷缓存 - 不常用Bean
    L4_ARCHIVE = "L4_ARCHIVE"  # L4归档缓存 - 很少访问的Bean


class CacheStrategy(Enum):
    """缓存策略枚举"""

    LRU = "LRU"  # 最近最少使用
    LFU = "LFU"  # 最少使用频率
    FIFO = "FIFO"  # 先进先出
    TTL = "TTL"  # 基于时间过期
    ADAPTIVE = "ADAPTIVE"  # 自适应策略


class BeanAccessPattern:
    """Bean访问模式分析"""

    def __init__(self, name: str):
        self.name = name
        self.access_count = 0
        self.last_access_time = time.time()
        self.first_access_time = time.time()
        self.access_intervals: list[float] = []
        self.creation_time = 0.0
        self.memory_size = 0

    def record_access(self) -> None:
        """记录访问"""
        current_time = time.time()
        if self.access_count > 0:
            interval = current_time - self.last_access_time
            self.access_intervals.append(interval)
            # 只保留最近20次访问间隔
            if len(self.access_intervals) > 20:
                self.access_intervals.pop(0)

        self.access_count += 1
        self.last_access_time = current_time

    def get_access_frequency(self) -> float:
        """获取访问频率 (次/秒)"""
        if self.access_count <= 1:
            return 0.0

        total_time = self.last_access_time - self.first_access_time
        if total_time <= 0:
            return 0.0

        return self.access_count / total_time

    def get_average_interval(self) -> float:
        """获取平均访问间隔"""
        if not self.access_intervals:
            return float("inf")
        return sum(self.access_intervals) / len(self.access_intervals)

    def is_hot(self) -> bool:
        """判断是否为热点Bean"""
        frequency = self.get_access_frequency()
        avg_interval = self.get_average_interval()

        # 热点判断条件：高频访问且访问间隔短
        return frequency > 0.1 and avg_interval < 10.0  # 每秒0.1次以上，间隔小于10秒

    def is_warm(self) -> bool:
        """判断是否为温Bean"""
        frequency = self.get_access_frequency()
        avg_interval = self.get_average_interval()

        # 温Bean判断条件：中等频率访问
        return frequency > 0.01 and avg_interval < 60.0  # 每秒0.01次以上，间隔小于1分钟

    def is_cold(self) -> bool:
        """判断是否为冷Bean"""
        current_time = time.time()
        time_since_last_access = current_time - self.last_access_time

        # 冷Bean判断条件：长时间未访问
        return time_since_last_access > 300.0  # 5分钟未访问


class LRUCache:
    """LRU缓存实现"""

    def __init__(self, max_size: int):
        self.max_size = max_size
        self._cache: OrderedDict[str, Any] = OrderedDict()
        self._lock = threading.RLock()

    def get(self, key: str) -> Optional[Any]:
        """获取缓存项"""
        with self._lock:
            if key in self._cache:
                # 移动到末尾（最近使用）
                value = self._cache.pop(key)
                self._cache[key] = value
                return value
            return None

    def put(self, key: str, value: Any) -> None:
        """存储缓存项"""
        with self._lock:
            if key in self._cache:
                # 更新现有项
                self._cache.pop(key)
                self._cache[key] = value
            else:
                # 添加新项
                if len(self._cache) >= self.max_size:
                    # 移除最久未使用的项
                    self._cache.popitem(last=False)
                self._cache[key] = value

    def remove(self, key: str) -> bool:
        """移除缓存项"""
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                return True
            return False

    def clear(self) -> None:
        """清空缓存"""
        with self._lock:
            self._cache.clear()

    def size(self) -> int:
        """获取缓存大小"""
        return len(self._cache)

    def keys(self) -> list[str]:
        """获取所有键"""
        with self._lock:
            return list(self._cache.keys())


class LFUCache:
    """LFU缓存实现"""

    def __init__(self, max_size: int):
        self.max_size = max_size
        self._cache: dict[str, Any] = {}
        self._frequencies: dict[str, int] = {}
        self._lock = threading.RLock()

    def get(self, key: str) -> Optional[Any]:
        """获取缓存项"""
        with self._lock:
            if key in self._cache:
                self._frequencies[key] += 1
                return self._cache[key]
            return None

    def put(self, key: str, value: Any) -> None:
        """存储缓存项"""
        with self._lock:
            if key in self._cache:
                # 更新现有项
                self._cache[key] = value
                self._frequencies[key] += 1
            else:
                # 添加新项
                if len(self._cache) >= self.max_size:
                    # 移除使用频率最低的项
                    min_freq_key = min(self._frequencies, key=self._frequencies.get)
                    del self._cache[min_freq_key]
                    del self._frequencies[min_freq_key]

                self._cache[key] = value
                self._frequencies[key] = 1

    def remove(self, key: str) -> bool:
        """移除缓存项"""
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                del self._frequencies[key]
                return True
            return False

    def clear(self) -> None:
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self._frequencies.clear()

    def size(self) -> int:
        """获取缓存大小"""
        return len(self._cache)


class TTLCache:
    """TTL缓存实现"""

    def __init__(self, max_size: int, default_ttl: float = 300.0):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self._cache: dict[str, Any] = {}
        self._expire_times: dict[str, float] = {}
        self._lock = threading.RLock()

    def get(self, key: str) -> Optional[Any]:
        """获取缓存项"""
        with self._lock:
            if key in self._cache:
                # 检查是否过期
                if time.time() > self._expire_times[key]:
                    del self._cache[key]
                    del self._expire_times[key]
                    return None
                return self._cache[key]
            return None

    def put(self, key: str, value: Any, ttl: Optional[float] = None) -> None:
        """存储缓存项"""
        with self._lock:
            if len(self._cache) >= self.max_size and key not in self._cache:
                # 清理过期项
                self._cleanup_expired()

                # 如果还是满的，移除最早过期的项
                if len(self._cache) >= self.max_size:
                    earliest_key = min(self._expire_times, key=self._expire_times.get)
                    del self._cache[earliest_key]
                    del self._expire_times[earliest_key]

            self._cache[key] = value
            self._expire_times[key] = time.time() + (ttl or self.default_ttl)

    def _cleanup_expired(self) -> None:
        """清理过期项"""
        current_time = time.time()
        expired_keys = [key for key, expire_time in self._expire_times.items() if current_time > expire_time]

        for key in expired_keys:
            del self._cache[key]
            del self._expire_times[key]

    def remove(self, key: str) -> bool:
        """移除缓存项"""
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                del self._expire_times[key]
                return True
            return False

    def clear(self) -> None:
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self._expire_times.clear()

    def size(self) -> int:
        """获取缓存大小"""
        return len(self._cache)


class CacheMetrics:
    """缓存性能指标"""

    def __init__(self):
        self.total_requests = 0
        self.cache_hits = 0
        self.cache_misses = 0
        self.l1_hits = 0
        self.l2_hits = 0
        self.l3_hits = 0
        self.l4_hits = 0
        self.evictions = 0
        self.promotions = 0  # 缓存级别提升次数
        self.demotions = 0  # 缓存级别降级次数
        self.total_memory_usage = 0
        self.average_access_time = 0.0
        self._lock = threading.RLock()

    def record_hit(self, level: CacheLevel) -> None:
        """记录缓存命中"""
        with self._lock:
            self.total_requests += 1
            self.cache_hits += 1

            if level == CacheLevel.L1_HOT:
                self.l1_hits += 1
            elif level == CacheLevel.L2_WARM:
                self.l2_hits += 1
            elif level == CacheLevel.L3_COLD:
                self.l3_hits += 1
            elif level == CacheLevel.L4_ARCHIVE:
                self.l4_hits += 1

    def record_miss(self) -> None:
        """记录缓存未命中"""
        with self._lock:
            self.total_requests += 1
            self.cache_misses += 1

    def record_eviction(self) -> None:
        """记录缓存驱逐"""
        with self._lock:
            self.evictions += 1

    def record_promotion(self) -> None:
        """记录缓存提升"""
        with self._lock:
            self.promotions += 1

    def record_demotion(self) -> None:
        """记录缓存降级"""
        with self._lock:
            self.demotions += 1

    def get_hit_rate(self) -> float:
        """获取缓存命中率"""
        if self.total_requests == 0:
            return 0.0
        return self.cache_hits / self.total_requests

    def get_stats(self) -> dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            return {
                "total_requests": self.total_requests,
                "cache_hits": self.cache_hits,
                "cache_misses": self.cache_misses,
                "hit_rate": self.get_hit_rate(),
                "l1_hits": self.l1_hits,
                "l2_hits": self.l2_hits,
                "l3_hits": self.l3_hits,
                "l4_hits": self.l4_hits,
                "evictions": self.evictions,
                "promotions": self.promotions,
                "demotions": self.demotions,
                "total_memory_usage": self.total_memory_usage,
                "average_access_time": self.average_access_time,
            }


class MultiLevelCacheManager:
    """多级缓存管理器 - 自适应缓存策略

    实现四级缓存架构：
    L1缓存: 热点Bean (内存, 100个, LRU) - 最频繁访问
    L2缓存: 温Bean (内存, 500个, LFU) - 常用Bean
    L3缓存: 冷Bean (内存, 2000个, TTL) - 不常用Bean
    L4缓存: 归档Bean (弱引用, 无限制) - 很少访问的Bean
    """

    def __init__(self, l1_size: int = 100, l2_size: int = 500, l3_size: int = 2000, l3_ttl: float = 300.0, enable_adaptive: bool = True):
        """初始化多级缓存管理器

        Args:
            l1_size: L1缓存大小（热点Bean）
            l2_size: L2缓存大小（温Bean）
            l3_size: L3缓存大小（冷Bean）
            l3_ttl: L3缓存TTL（秒）
            enable_adaptive: 是否启用自适应策略
        """
        # 四级缓存
        self._l1_cache = LRUCache(l1_size)  # 热点缓存
        self._l2_cache = LFUCache(l2_size)  # 温缓存
        self._l3_cache = TTLCache(l3_size, l3_ttl)  # 冷缓存
        self._l4_cache: dict[str, weakref.ref] = {}  # 归档缓存（弱引用）

        # 访问模式分析器
        self._access_patterns: dict[str, BeanAccessPattern] = {}

        # 缓存级别映射
        self._bean_levels: dict[str, CacheLevel] = {}

        # 性能指标
        self._metrics = CacheMetrics()

        # 配置
        self.enable_adaptive = enable_adaptive

        # 线程安全锁
        self._lock = threading.RLock()

        # 后台任务
        self._cleanup_task: Optional[asyncio.Task] = None
        self._rebalance_task: Optional[asyncio.Task] = None

        logger.info(f"MultiLevelCacheManager initialized: L1={l1_size}, L2={l2_size}, L3={l3_size}, TTL={l3_ttl}s")

    def get_bean(self, name: str) -> Optional[Any]:
        """多级缓存获取Bean

        按照 L1 -> L2 -> L3 -> L4 的顺序查找Bean，
        找到后根据访问模式可能提升缓存级别。

        Args:
            name: Bean名称

        Returns:
            Bean实例，如果不存在返回None
        """
        start_time = time.time()

        with self._lock:
            # 记录访问模式
            if name not in self._access_patterns:
                self._access_patterns[name] = BeanAccessPattern(name)

            pattern = self._access_patterns[name]
            pattern.record_access()

            # L1缓存查找（热点）
            bean = self._l1_cache.get(name)
            if bean is not None:
                self._metrics.record_hit(CacheLevel.L1_HOT)
                self._bean_levels[name] = CacheLevel.L1_HOT
                self._update_access_time(start_time)
                return bean

            # L2缓存查找（温）
            bean = self._l2_cache.get(name)
            if bean is not None:
                self._metrics.record_hit(CacheLevel.L2_WARM)
                self._bean_levels[name] = CacheLevel.L2_WARM

                # 如果变成热点，提升到L1
                if self.enable_adaptive and pattern.is_hot():
                    self._promote_to_l1(name, bean)

                self._update_access_time(start_time)
                return bean

            # L3缓存查找（冷）
            bean = self._l3_cache.get(name)
            if bean is not None:
                self._metrics.record_hit(CacheLevel.L3_COLD)
                self._bean_levels[name] = CacheLevel.L3_COLD

                # 根据访问模式可能提升缓存级别
                if self.enable_adaptive:
                    if pattern.is_hot():
                        self._promote_to_l1(name, bean)
                    elif pattern.is_warm():
                        self._promote_to_l2(name, bean)

                self._update_access_time(start_time)
                return bean

            # L4缓存查找（归档）
            if name in self._l4_cache:
                weak_ref = self._l4_cache[name]
                bean = weak_ref()
                if bean is not None:
                    self._metrics.record_hit(CacheLevel.L4_ARCHIVE)
                    self._bean_levels[name] = CacheLevel.L4_ARCHIVE

                    # 重新激活Bean，提升到合适级别
                    if self.enable_adaptive:
                        if pattern.is_hot():
                            self._promote_to_l1(name, bean)
                        elif pattern.is_warm():
                            self._promote_to_l2(name, bean)
                        else:
                            self._promote_to_l3(name, bean)

                    self._update_access_time(start_time)
                    return bean
                else:
                    # 弱引用已失效，清理
                    del self._l4_cache[name]

            # 缓存未命中
            self._metrics.record_miss()
            self._update_access_time(start_time)
            return None

    def put_bean(self, name: str, bean: Any) -> None:
        """存储Bean到多级缓存

        根据Bean的特性和访问模式选择合适的缓存级别。

        Args:
            name: Bean名称
            bean: Bean实例
        """
        with self._lock:
            # 获取或创建访问模式
            if name not in self._access_patterns:
                self._access_patterns[name] = BeanAccessPattern(name)

            pattern = self._access_patterns[name]

            # 根据访问模式选择缓存级别
            if self.enable_adaptive:
                if pattern.is_hot():
                    self._put_to_l1(name, bean)
                elif pattern.is_warm():
                    self._put_to_l2(name, bean)
                else:
                    self._put_to_l3(name, bean)
            else:
                # 默认放入L3缓存
                self._put_to_l3(name, bean)

    def remove_bean(self, name: str) -> bool:
        """从所有缓存级别移除Bean

        Args:
            name: Bean名称

        Returns:
            是否成功移除
        """
        with self._lock:
            removed = False

            # 从所有级别移除
            if self._l1_cache.remove(name):
                removed = True
            if self._l2_cache.remove(name):
                removed = True
            if self._l3_cache.remove(name):
                removed = True
            if name in self._l4_cache:
                del self._l4_cache[name]
                removed = True

            # 清理访问模式和级别映射
            if name in self._access_patterns:
                del self._access_patterns[name]
            if name in self._bean_levels:
                del self._bean_levels[name]

            return removed

    def clear(self) -> None:
        """清空所有缓存"""
        with self._lock:
            self._l1_cache.clear()
            self._l2_cache.clear()
            self._l3_cache.clear()
            self._l4_cache.clear()
            self._access_patterns.clear()
            self._bean_levels.clear()
            logger.info("All cache levels cleared")

    def _promote_to_l1(self, name: str, bean: Any) -> None:
        """提升Bean到L1缓存"""
        self._l1_cache.put(name, bean)
        self._bean_levels[name] = CacheLevel.L1_HOT
        self._metrics.record_promotion()

        # 从其他级别移除
        self._l2_cache.remove(name)
        self._l3_cache.remove(name)
        if name in self._l4_cache:
            del self._l4_cache[name]

        logger.debug(f"Bean '{name}' promoted to L1 cache")

    def _promote_to_l2(self, name: str, bean: Any) -> None:
        """提升Bean到L2缓存"""
        self._l2_cache.put(name, bean)
        self._bean_levels[name] = CacheLevel.L2_WARM
        self._metrics.record_promotion()

        # 从其他级别移除
        self._l3_cache.remove(name)
        if name in self._l4_cache:
            del self._l4_cache[name]

        logger.debug(f"Bean '{name}' promoted to L2 cache")

    def _promote_to_l3(self, name: str, bean: Any) -> None:
        """提升Bean到L3缓存"""
        self._l3_cache.put(name, bean)
        self._bean_levels[name] = CacheLevel.L3_COLD
        self._metrics.record_promotion()

        # 从L4移除
        if name in self._l4_cache:
            del self._l4_cache[name]

        logger.debug(f"Bean '{name}' promoted to L3 cache")

    def _put_to_l1(self, name: str, bean: Any) -> None:
        """存储Bean到L1缓存"""
        self._l1_cache.put(name, bean)
        self._bean_levels[name] = CacheLevel.L1_HOT

    def _put_to_l2(self, name: str, bean: Any) -> None:
        """存储Bean到L2缓存"""
        self._l2_cache.put(name, bean)
        self._bean_levels[name] = CacheLevel.L2_WARM

    def _put_to_l3(self, name: str, bean: Any) -> None:
        """存储Bean到L3缓存"""
        self._l3_cache.put(name, bean)
        self._bean_levels[name] = CacheLevel.L3_COLD

    def _demote_to_l4(self, name: str, bean: Any) -> None:
        """降级Bean到L4缓存（弱引用）"""
        self._l4_cache[name] = weakref.ref(bean)
        self._bean_levels[name] = CacheLevel.L4_ARCHIVE
        self._metrics.record_demotion()
        logger.debug(f"Bean '{name}' demoted to L4 cache")

    def _update_access_time(self, start_time: float) -> None:
        """更新平均访问时间"""
        access_time = time.time() - start_time
        # 简单的移动平均
        if self._metrics.average_access_time == 0:
            self._metrics.average_access_time = access_time
        else:
            self._metrics.average_access_time = self._metrics.average_access_time * 0.9 + access_time * 0.1

    def get_cache_stats(self) -> dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            stats = self._metrics.get_stats()

            # 添加缓存级别统计
            stats.update(
                {
                    "cache_levels": {
                        "l1_size": self._l1_cache.size(),
                        "l2_size": self._l2_cache.size(),
                        "l3_size": self._l3_cache.size(),
                        "l4_size": len(self._l4_cache),
                        "total_beans": len(self._access_patterns),
                    },
                    "level_distribution": {level.value: sum(1 for l in self._bean_levels.values() if l == level) for level in CacheLevel},
                    "adaptive_enabled": self.enable_adaptive,
                }
            )

            return stats

    def get_bean_info(self, name: str) -> Optional[dict[str, Any]]:
        """获取Bean的缓存信息

        Args:
            name: Bean名称

        Returns:
            Bean的缓存信息，如果不存在返回None
        """
        with self._lock:
            if name not in self._access_patterns:
                return None

            pattern = self._access_patterns[name]
            level = self._bean_levels.get(name, None)

            return {
                "name": name,
                "cache_level": level.value if level else None,
                "access_count": pattern.access_count,
                "access_frequency": pattern.get_access_frequency(),
                "average_interval": pattern.get_average_interval(),
                "last_access_time": pattern.last_access_time,
                "first_access_time": pattern.first_access_time,
                "is_hot": pattern.is_hot(),
                "is_warm": pattern.is_warm(),
                "is_cold": pattern.is_cold(),
            }

    def get_all_beans_info(self) -> list[dict[str, Any]]:
        """获取所有Bean的缓存信息"""
        with self._lock:
            result = []
            for name in self._access_patterns:
                bean_info = self.get_bean_info(name)
                if bean_info is not None:
                    result.append(bean_info)
            return result

    def rebalance_cache(self) -> None:
        """重新平衡缓存

        根据访问模式重新分配Bean到合适的缓存级别
        """
        if not self.enable_adaptive:
            return

        with self._lock:
            rebalanced_count = 0

            for name, pattern in self._access_patterns.items():
                current_level = self._bean_levels.get(name)
                if current_level is None:
                    continue

                # 获取Bean实例
                bean = None
                if current_level == CacheLevel.L1_HOT:
                    bean = self._l1_cache.get(name)
                elif current_level == CacheLevel.L2_WARM:
                    bean = self._l2_cache.get(name)
                elif current_level == CacheLevel.L3_COLD:
                    bean = self._l3_cache.get(name)
                elif current_level == CacheLevel.L4_ARCHIVE:
                    if name in self._l4_cache:
                        weak_ref = self._l4_cache[name]
                        bean = weak_ref()

                if bean is None:
                    continue

                # 根据访问模式重新分配
                target_level = None
                if pattern.is_hot():
                    target_level = CacheLevel.L1_HOT
                elif pattern.is_warm():
                    target_level = CacheLevel.L2_WARM
                elif pattern.is_cold():
                    target_level = CacheLevel.L4_ARCHIVE
                else:
                    target_level = CacheLevel.L3_COLD

                # 如果需要调整级别
                if target_level != current_level:
                    # 从当前级别移除
                    self.remove_bean(name)

                    # 添加到目标级别
                    if target_level == CacheLevel.L1_HOT:
                        self._put_to_l1(name, bean)
                    elif target_level == CacheLevel.L2_WARM:
                        self._put_to_l2(name, bean)
                    elif target_level == CacheLevel.L3_COLD:
                        self._put_to_l3(name, bean)
                    elif target_level == CacheLevel.L4_ARCHIVE:
                        self._demote_to_l4(name, bean)

                    rebalanced_count += 1

            if rebalanced_count > 0:
                logger.info(f"Cache rebalanced: {rebalanced_count} beans moved")

    def cleanup_expired(self) -> None:
        """清理过期和无效的缓存项"""
        with self._lock:
            cleaned_count = 0

            # 清理L4缓存中的无效弱引用
            invalid_refs = []
            for name, weak_ref in self._l4_cache.items():
                if weak_ref() is None:
                    invalid_refs.append(name)

            for name in invalid_refs:
                del self._l4_cache[name]
                if name in self._bean_levels:
                    del self._bean_levels[name]
                if name in self._access_patterns:
                    del self._access_patterns[name]
                cleaned_count += 1

            # 清理长时间未访问的访问模式记录
            current_time = time.time()
            inactive_patterns = []
            for name, pattern in self._access_patterns.items():
                if current_time - pattern.last_access_time > 3600:  # 1小时未访问
                    inactive_patterns.append(name)

            for name in inactive_patterns:
                if name not in self._bean_levels:  # 确保Bean不在任何缓存中
                    del self._access_patterns[name]
                    cleaned_count += 1

            if cleaned_count > 0:
                logger.info(f"Cache cleanup completed: {cleaned_count} items removed")

    def start_background_tasks(self) -> None:
        """启动后台任务"""
        if self._cleanup_task is None or self._cleanup_task.done():
            self._cleanup_task = asyncio.create_task(self._background_cleanup())

        if self._rebalance_task is None or self._rebalance_task.done():
            self._rebalance_task = asyncio.create_task(self._background_rebalance())

        logger.info("Background cache tasks started")

    def stop_background_tasks(self) -> None:
        """停止后台任务"""
        if self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()

        if self._rebalance_task and not self._rebalance_task.done():
            self._rebalance_task.cancel()

        logger.info("Background cache tasks stopped")

    async def _background_cleanup(self) -> None:
        """后台清理任务"""
        while True:
            try:
                await asyncio.sleep(300)  # 5分钟执行一次
                self.cleanup_expired()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Background cleanup error: {e}")

    async def _background_rebalance(self) -> None:
        """后台重平衡任务"""
        while True:
            try:
                await asyncio.sleep(600)  # 10分钟执行一次
                self.rebalance_cache()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Background rebalance error: {e}")

    def optimize_cache_sizes(self) -> None:
        """根据使用情况优化缓存大小"""
        with self._lock:
            stats = self.get_cache_stats()

            # 分析各级别的命中率
            total_hits = stats["cache_hits"]
            if total_hits == 0:
                return

            l1_hit_rate = stats["l1_hits"] / total_hits
            l2_hit_rate = stats["l2_hits"] / total_hits
            l3_hit_rate = stats["l3_hits"] / total_hits

            # 根据命中率调整缓存大小建议
            recommendations = []

            if l1_hit_rate > 0.6:  # L1命中率过高，可能需要扩大
                recommendations.append("Consider increasing L1 cache size")
            elif l1_hit_rate < 0.3:  # L1命中率过低，可能需要缩小
                recommendations.append("Consider decreasing L1 cache size")

            if l2_hit_rate > 0.3:
                recommendations.append("L2 cache is performing well")
            elif l2_hit_rate < 0.1:
                recommendations.append("Consider optimizing L2 cache strategy")

            if l3_hit_rate > 0.2:
                recommendations.append("Consider increasing L3 cache TTL")

            logger.info(f"Cache optimization recommendations: {recommendations}")

    def export_cache_report(self) -> dict[str, Any]:
        """导出详细的缓存报告"""
        with self._lock:
            stats = self.get_cache_stats()
            beans_info = self.get_all_beans_info()

            # 按缓存级别分组Bean信息
            beans_by_level: dict[str, list[dict[str, Any]]] = {}
            for bean_info in beans_info:
                level = bean_info["cache_level"]
                if level not in beans_by_level:
                    beans_by_level[level] = []
                beans_by_level[level].append(bean_info)

            # 计算性能指标
            hit_rate = stats["hit_rate"]

            performance_grade = "A"
            if hit_rate < 0.8:
                performance_grade = "B"
            if hit_rate < 0.6:
                performance_grade = "C"
            if hit_rate < 0.4:
                performance_grade = "D"

            return {
                "timestamp": time.time(),
                "performance_grade": performance_grade,
                "overall_stats": stats,
                "beans_by_level": dict(beans_by_level),
                "top_accessed_beans": sorted(beans_info, key=lambda x: x["access_count"], reverse=True)[:10],
                "recommendations": self._generate_recommendations(stats, beans_info),
            }

    def _generate_recommendations(self, stats: dict[str, Any], beans_info: list[dict[str, Any]]) -> list[str]:
        """生成缓存优化建议"""
        recommendations = []

        hit_rate = stats["hit_rate"]
        if hit_rate < 0.7:
            recommendations.append("Overall cache hit rate is low, consider enabling adaptive mode")

        if stats["l1_hits"] / max(stats["cache_hits"], 1) < 0.3:
            recommendations.append("L1 cache hit rate is low, review hot bean identification")

        if len([b for b in beans_info if b["is_hot"]]) > stats["cache_levels"]["l1_size"]:
            recommendations.append("Too many hot beans for L1 cache size, consider increasing L1 size")

        if stats["evictions"] > stats["cache_hits"] * 0.1:
            recommendations.append("High eviction rate detected, consider increasing cache sizes")

        return recommendations
