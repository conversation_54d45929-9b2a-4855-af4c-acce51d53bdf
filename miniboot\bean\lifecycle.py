#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Bean生命周期管理系统
"""

import contextlib
import threading
from typing import Any, Callable, Optional

from .base import (
    ApplicationContextAware,
    BeanFactoryAware,
    BeanNameAware,
    BeanPostProcessor,
    DisposableBean,
    InitializingBean,
    Lifecycle,
    SmartLifecycle,
)


class LifecycleManager:
    """Bean生命周期管理器

    统一管理Bean的完整生命周期，包括：
    1. Bean实例化后的感知接口调用
    2. 依赖注入完成后的初始化
    3. Bean后置处理器的执行
    4. 生命周期Bean的启动和停止
    5. Bean销毁时的清理工作

    这是Bean工厂的核心组件，确保Bean按照正确的顺序完成生命周期。
    """

    def __init__(self, bean_factory: Optional["BeanFactory"] = None):
        """初始化生命周期管理器

        Args:
            bean_factory: Bean工厂实例，用于获取其他Bean
        """
        self._bean_factory = bean_factory
        self._lock = threading.RLock()

        # Bean后置处理器列表（按优先级排序）
        self._post_processors: list[BeanPostProcessor] = []

        # 生命周期Bean管理
        self._lifecycle_beans: dict[str, Lifecycle] = {}
        self._smart_lifecycle_beans: dict[str, SmartLifecycle] = {}

        # 生命周期状态
        self._running = False
        self._shutdown_hooks: list[Callable] = []

        # 统计信息
        self._lifecycle_stats = {
            "beans_initialized": 0,
            "beans_destroyed": 0,
            "lifecycle_beans_started": 0,
            "lifecycle_beans_stopped": 0,
            "post_processor_executions": 0,
        }

    def apply_bean_post_processors_before_initialization(self, bean: Any, bean_name: str) -> Any:
        """在Bean初始化前应用后置处理器

        Args:
            bean: Bean实例
            bean_name: Bean名称

        Returns:
            Any: 处理后的Bean实例
        """
        current_bean = bean

        for processor in self._post_processors:
            try:
                processed_bean = processor.post_process_before_initialization(current_bean, bean_name)
                if processed_bean is not None:
                    current_bean = processed_bean

                self._lifecycle_stats["post_processor_executions"] += 1

            except Exception as e:
                raise RuntimeError(
                    f"BeanPostProcessor {processor.__class__.__name__} failed to process bean '{bean_name}' before initialization: {str(e)}"
                ) from e

        return current_bean

    def apply_bean_post_processors_after_initialization(self, bean: Any, bean_name: str) -> Any:
        """在Bean初始化后应用后置处理器

        Args:
            bean: Bean实例
            bean_name: Bean名称

        Returns:
            Any: 处理后的Bean实例
        """
        current_bean = bean

        for processor in self._post_processors:
            try:
                processed_bean = processor.post_process_after_initialization(current_bean, bean_name)
                if processed_bean is not None:
                    current_bean = processed_bean

                self._lifecycle_stats["post_processor_executions"] += 1

            except Exception as e:
                raise RuntimeError(
                    f"BeanPostProcessor {processor.__class__.__name__} failed to process bean '{bean_name}' after initialization: {str(e)}"
                ) from e

        return current_bean

    def initialize_bean(self, bean: Any, bean_name: str, bean_definition: "BeanDefinition") -> Any:
        """完整的Bean初始化流程

        执行完整的Bean初始化序列：
        1. 感知接口调用
        2. 初始化前后置处理器
        3. 初始化方法调用
        4. 初始化后后置处理器

        Args:
            bean: Bean实例
            bean_name: Bean名称
            bean_definition: Bean定义

        Returns:
            Any: 完全初始化的Bean实例
        """
        try:
            # 1. 调用感知接口
            self._invoke_aware_interfaces(bean, bean_name)

            # 2. 初始化前后置处理器
            bean = self.apply_bean_post_processors_before_initialization(bean, bean_name)

            # 3. 调用初始化方法
            self._invoke_init_methods(bean, bean_definition)

            # 4. 初始化后后置处理器
            bean = self.apply_bean_post_processors_after_initialization(bean, bean_name)

            # 5. 注册生命周期Bean
            self._register_lifecycle_bean(bean, bean_name)

            self._lifecycle_stats["beans_initialized"] += 1

            return bean

        except Exception as e:
            raise RuntimeError(f"Failed to initialize bean '{bean_name}': {str(e)}") from e

    def destroy_bean(self, bean: Any, bean_name: str) -> None:
        """销毁Bean

        执行Bean的销毁流程：
        1. 停止生命周期Bean
        2. 调用销毁方法
        3. 清理资源

        Args:
            bean: Bean实例
            bean_name: Bean名称
        """
        try:
            # 1. 停止生命周期Bean
            self._unregister_lifecycle_bean(bean_name)

            # 2. 调用销毁方法
            if isinstance(bean, DisposableBean):
                bean.destroy()

            self._lifecycle_stats["beans_destroyed"] += 1

        except Exception:
            # 销毁过程中的异常不应该阻止其他Bean的销毁
            pass

    def _invoke_aware_interfaces(self, bean: Any, bean_name: str) -> None:
        """调用感知接口

        Args:
            bean: Bean实例
            bean_name: Bean名称
        """
        # Bean名称感知
        if isinstance(bean, BeanNameAware):
            bean.set_bean_name(bean_name)

        # Bean工厂感知
        if isinstance(bean, BeanFactoryAware) and self._bean_factory:
            bean.set_bean_factory(self._bean_factory)

        # 应用上下文感知
        if isinstance(bean, ApplicationContextAware) and hasattr(self._bean_factory, "application_context"):
            bean.set_application_context(self._bean_factory.application_context)

    def _invoke_init_methods(self, bean: Any, bean_definition: "BeanDefinition") -> None:
        """调用初始化方法

        Args:
            bean: Bean实例
            bean_definition: Bean定义
        """
        # 1. InitializingBean接口
        if isinstance(bean, InitializingBean):
            bean.after_properties_set()

        # 2. 自定义初始化方法
        if bean_definition.has_init():
            init_method = getattr(bean, bean_definition.init_method_name, None)
            if init_method and callable(init_method):
                init_method()

    def _register_lifecycle_bean(self, bean: Any, bean_name: str) -> None:
        """注册生命周期Bean

        Args:
            bean: Bean实例
            bean_name: Bean名称
        """
        if isinstance(bean, SmartLifecycle):
            with self._lock:
                self._smart_lifecycle_beans[bean_name] = bean
        elif isinstance(bean, Lifecycle):
            with self._lock:
                self._lifecycle_beans[bean_name] = bean

    def _unregister_lifecycle_bean(self, bean_name: str) -> None:
        """注销生命周期Bean

        Args:
            bean_name: Bean名称
        """
        with self._lock:
            # 先停止Bean
            if bean_name in self._smart_lifecycle_beans:
                bean = self._smart_lifecycle_beans.pop(bean_name)
                if bean.is_running():
                    bean.stop()
                    self._lifecycle_stats["lifecycle_beans_stopped"] += 1
            elif bean_name in self._lifecycle_beans:
                bean = self._lifecycle_beans.pop(bean_name)
                if bean.is_running():
                    bean.stop()
                    self._lifecycle_stats["lifecycle_beans_stopped"] += 1

    def add_bean_post_processor(self, processor: BeanPostProcessor) -> None:
        """添加Bean后置处理器

        Args:
            processor: Bean后置处理器
        """
        if processor not in self._post_processors:
            self._post_processors.append(processor)
            # 按优先级排序
            self._post_processors.sort(key=lambda p: getattr(p, "get_order", lambda: 0)())

    def remove_bean_post_processor(self, processor: BeanPostProcessor) -> bool:
        """移除Bean后置处理器

        Args:
            processor: Bean后置处理器

        Returns:
            bool: 如果成功移除返回True，否则返回False
        """
        try:
            self._post_processors.remove(processor)
            return True
        except ValueError:
            return False

    def get_bean_post_processors(self) -> list[BeanPostProcessor]:
        """获取所有Bean后置处理器

        Returns:
            List[BeanPostProcessor]: Bean后置处理器列表
        """
        return self._post_processors.copy()

    def start_beans(self) -> None:
        """启动所有生命周期Bean

        按照阶段顺序启动SmartLifecycle Bean，然后启动普通Lifecycle Bean。
        """
        if self._running:
            return

        with self._lock:
            # 1. 启动SmartLifecycle Bean（按阶段排序）
            smart_beans = list(self._smart_lifecycle_beans.items())
            smart_beans.sort(key=lambda item: item[1].get_phase())

            for bean_name, bean in smart_beans:
                if bean.is_auto_startup() and not bean.is_running():
                    try:
                        bean.start()
                        self._lifecycle_stats["lifecycle_beans_started"] += 1
                    except Exception:
                        # 记录错误但继续启动其他Bean
                        pass

            # 2. 启动普通Lifecycle Bean
            for bean_name, bean in self._lifecycle_beans.items():
                if not bean.is_running():
                    try:
                        bean.start()
                        self._lifecycle_stats["lifecycle_beans_started"] += 1
                    except Exception:
                        # 记录错误但继续启动其他Bean
                        pass

            self._running = True

    def stop_beans(self) -> None:
        """停止所有生命周期Bean

        按照阶段逆序停止SmartLifecycle Bean，然后停止普通Lifecycle Bean。
        """
        if not self._running:
            return

        with self._lock:
            # 1. 停止SmartLifecycle Bean（按阶段逆序）
            smart_beans = list(self._smart_lifecycle_beans.items())
            smart_beans.sort(key=lambda item: item[1].get_phase(), reverse=True)

            for bean_name, bean in smart_beans:
                if bean.is_running():
                    try:
                        bean.stop()
                        self._lifecycle_stats["lifecycle_beans_stopped"] += 1
                    except Exception:
                        # 记录错误但继续停止其他Bean
                        pass

            # 2. 停止普通Lifecycle Bean
            for bean_name, bean in self._lifecycle_beans.items():
                if bean.is_running():
                    try:
                        bean.stop()
                        self._lifecycle_stats["lifecycle_beans_stopped"] += 1
                    except Exception:
                        # 记录错误但继续停止其他Bean
                        pass

            self._running = False

    def is_running(self) -> bool:
        """检查生命周期管理器是否正在运行

        Returns:
            bool: 如果正在运行返回True，否则返回False
        """
        return self._running

    def add_shutdown_hook(self, hook: Callable) -> None:
        """添加关闭钩子

        Args:
            hook: 关闭时要执行的函数
        """
        if hook not in self._shutdown_hooks:
            self._shutdown_hooks.append(hook)

    def remove_shutdown_hook(self, hook: Callable) -> bool:
        """移除关闭钩子

        Args:
            hook: 要移除的关闭钩子

        Returns:
            bool: 如果成功移除返回True，否则返回False
        """
        try:
            self._shutdown_hooks.remove(hook)
            return True
        except ValueError:
            return False

    def shutdown(self) -> None:
        """关闭生命周期管理器

        执行完整的关闭流程：
        1. 停止所有生命周期Bean
        2. 执行关闭钩子
        3. 清理资源
        """
        try:
            # 1. 停止所有生命周期Bean
            self.stop_beans()

            # 2. 执行关闭钩子
            for hook in self._shutdown_hooks:
                with contextlib.suppress(Exception):
                    hook()

            # 3. 清理资源
            with self._lock:
                self._lifecycle_beans.clear()
                self._smart_lifecycle_beans.clear()
                self._post_processors.clear()
                self._shutdown_hooks.clear()

        except Exception:
            # 关闭过程中的异常不应该阻止关闭
            pass

    def get_lifecycle_stats(self) -> dict[str, int]:
        """获取生命周期统计信息

        Returns:
            Dict[str, int]: 生命周期统计信息
        """
        return self._lifecycle_stats.copy()

    def get_lifecycle_beans_info(self) -> dict[str, Any]:
        """获取生命周期Bean信息

        Returns:
            Dict[str, Any]: 生命周期Bean的详细信息
        """
        with self._lock:
            return {
                "lifecycle_beans_count": len(self._lifecycle_beans),
                "smart_lifecycle_beans_count": len(self._smart_lifecycle_beans),
                "lifecycle_bean_names": list(self._lifecycle_beans.keys()),
                "smart_lifecycle_bean_names": list(self._smart_lifecycle_beans.keys()),
                "post_processors_count": len(self._post_processors),
                "is_running": self._running,
                "stats": self._lifecycle_stats.copy(),
            }
