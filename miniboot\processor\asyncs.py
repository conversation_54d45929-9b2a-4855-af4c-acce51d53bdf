#!/usr/bin/env python
"""
异步处理器模块

提供 Spring Boot 风格的异步处理器，采用懒加载模式：
1. 只有检测到异步注解时才初始化线程池
2. 正确的依赖方向：processor -> asyncs
3. 符合 Spring Boot 的设计理念

迁移自 miniboot.asyncs.processor，现在位于正确的模块位置。
"""

from typing import Any, Optional

from loguru import logger

from .base import BeanPostProcessor, OrderedBeanPostProcessor, ProcessorOrder


class AsyncAnnotationDetector:
    """异步注解检测器

    专门负责检测Bean中的异步注解和扫描异步方法。
    职责单一：只处理注解检测相关的逻辑。
    """

    def has_async_annotation(self, bean: Any) -> bool:
        """检查Bean是否有异步注解

        Args:
            bean: 要检查的Bean对象

        Returns:
            bool: 如果Bean有异步注解返回True，否则返回False
        """
        try:
            from ..annotations.schedule import is_async_enabled
            return is_async_enabled(bean.__class__)
        except Exception as e:
            logger.debug(f"Failed to check async annotation: {e}")
            return False

    def scan_async_methods(self, bean: Any) -> dict:
        """扫描Bean中的异步方法

        Args:
            bean: 要扫描的Bean对象

        Returns:
            dict: 异步方法信息字典，格式为 {method_name: {pool: str, timeout: float}}
        """
        async_methods = {}

        try:
            # 获取Bean类的所有方法
            for attr_name in dir(bean):
                if attr_name.startswith('_'):
                    continue

                attr = getattr(bean, attr_name)
                if not callable(attr):
                    continue

                # 检查是否有异步注解
                if hasattr(attr, '__is_async__') and attr.__is_async__:
                    async_methods[attr_name] = {
                        'pool': getattr(attr, '__async_pool__', None),
                        'timeout': getattr(attr, '__async_timeout__', None)
                    }

        except Exception as e:
            logger.debug(f"Error scanning async methods: {e}")

        return async_methods


class AsyncComponentInitializer:
    """异步组件初始化器

    专门负责异步组件的懒加载初始化。
    职责单一：只处理异步组件的初始化逻辑。
    """

    def __init__(self):
        """初始化异步组件初始化器"""
        self._thread_pool_manager = None
        self._async_properties = None
        self._initialized = False

    def initialize_if_needed(self) -> bool:
        """懒加载初始化异步组件

        只有在检测到异步注解时才执行初始化。

        Returns:
            bool: 初始化成功返回True，失败或已初始化返回False
        """
        if self._initialized:
            return True

        try:
            logger.info("Detected async annotations, initializing async components...")

            # 加载异步配置属性
            self._load_async_properties()

            # 检查异步功能是否启用
            if not self._async_properties.enabled:
                logger.info("Async functionality is disabled in configuration")
                return False

            # 初始化线程池管理器
            self._initialize_thread_pool_manager()

            # 创建默认线程池
            self._create_default_thread_pool()

            self._initialized = True
            logger.info("✅ Async components initialized successfully (lazy loading)")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize async components: {e}")
            return False

    def get_thread_pool_manager(self) -> Optional['ThreadPoolManager']:
        """获取线程池管理器

        Returns:
            ThreadPoolManager: 线程池管理器实例，未初始化时返回None
        """
        return self._thread_pool_manager

    def get_async_properties(self) -> Optional['AsyncProperties']:
        """获取异步配置属性

        Returns:
            AsyncProperties: 异步配置属性实例，未初始化时返回None
        """
        return self._async_properties

    def _load_async_properties(self) -> None:
        """加载异步配置属性"""
        try:
            from ..asyncs.properties import AsyncProperties, default_properties

            # 尝试从Bean工厂获取环境配置
            environment = self._get_environment()

            # 加载异步配置属性
            if environment:
                self._async_properties = AsyncProperties.from_environment(environment)
                logger.debug("Loaded async properties from environment")
            else:
                self._async_properties = default_properties()
                logger.debug("Using default async properties (no environment)")

        except Exception as e:
            logger.warning(f"Failed to load async properties: {e}, using defaults")
            from ..asyncs.properties import default_properties
            self._async_properties = default_properties()

    def _get_environment(self):
        """获取环境配置"""
        # 这里可以通过依赖注入或其他方式获取环境配置
        # 暂时返回None，使用默认配置
        return None

    def _initialize_thread_pool_manager(self) -> None:
        """初始化线程池管理器"""
        from ..asyncs.pool import ThreadPoolManager
        self._thread_pool_manager = ThreadPoolManager()

    def _create_default_thread_pool(self) -> None:
        """创建默认线程池"""
        try:
            from ..asyncs.executor import ThreadPoolAsyncExecutor
            from ..asyncs.pool import ThreadPoolConfig

            # 使用已加载的配置属性
            executor_props = self._async_properties.executor

            # 创建线程池配置
            pool_config = ThreadPoolConfig(
                core_size=executor_props.core_size,
                max_size=executor_props.max_size,
                queue_capacity=executor_props.queue_capacity,
                keep_alive=executor_props.keep_alive,
                thread_name_prefix=executor_props.thread_name_prefix,
                allow_core_thread_timeout=executor_props.allow_core_thread_timeout
            )

            # 创建并注册默认执行器
            executor = ThreadPoolAsyncExecutor(pool_config)
            self._thread_pool_manager.register_pool("default", executor.get_thread_pool())

            logger.debug("Created default thread pool for async processing")

        except Exception as e:
            logger.error(f"Failed to create default thread pool: {e}")


class AsyncMethodInterceptor:
    """异步方法拦截器

    专门负责异步方法的拦截和执行。
    职责单一：只处理方法拦截和异步执行逻辑。
    """

    def __init__(self, initializer: AsyncComponentInitializer):
        """初始化异步方法拦截器

        Args:
            initializer: 异步组件初始化器实例
        """
        self._initializer = initializer

    def create_intercepted_method(self, method: Any, pool_name: Optional[str] = None,
                                timeout: Optional[float] = None) -> Any:
        """创建拦截的异步方法

        Args:
            method: 原始方法
            pool_name: 线程池名称
            timeout: 超时时间

        Returns:
            Any: 拦截后的方法
        """
        import functools

        @functools.wraps(method)
        def wrapper(*args, **kwargs):
            return self._execute_async_method(method, args, kwargs, pool_name, timeout)

        # 保留原始方法信息
        wrapper.__async_method__ = True
        wrapper.__async_pool__ = pool_name or "default"
        wrapper.__async_timeout__ = timeout
        wrapper.__original_method__ = method

        return wrapper

    def _execute_async_method(self, method: Any, args: tuple, kwargs: dict,
                            pool_name: Optional[str], timeout: Optional[float]) -> Any:
        """执行异步方法

        Args:
            method: 要执行的方法
            args: 方法位置参数
            kwargs: 方法关键字参数
            pool_name: 线程池名称
            timeout: 超时时间

        Returns:
            Any: 方法执行结果
        """
        try:
            # 确定使用的线程池
            executor_name = pool_name or "default"

            # 获取线程池管理器
            thread_pool_manager = self._initializer.get_thread_pool_manager()
            if not thread_pool_manager:
                logger.error("Thread pool manager not initialized, executing synchronously")
                return method(*args, **kwargs)

            # 获取线程池
            pool = thread_pool_manager.get_pool(executor_name)
            if not pool:
                logger.warning(f"Thread pool '{executor_name}' not found, using default")
                pool = thread_pool_manager.get_pool("default")

            if not pool:
                logger.error("No thread pool available, executing synchronously")
                return method(*args, **kwargs)

            # 提交任务到线程池
            future = thread_pool_manager.submit_task(
                method, *args,
                pool_name=executor_name,
                timeout=timeout,
                **kwargs
            )

            # 等待结果
            return future.result(timeout=timeout)

        except Exception as e:
            logger.error(f"Failed to execute async method: {e}")
            # 降级到同步执行
            return method(*args, **kwargs)


class AsyncBeanProxyCreator:
    """异步Bean代理创建器

    专门负责创建异步代理对象。
    职责单一：只处理Bean代理创建逻辑。
    """

    def __init__(self, detector: AsyncAnnotationDetector, interceptor: AsyncMethodInterceptor):
        """初始化异步Bean代理创建器

        Args:
            detector: 异步注解检测器实例
            interceptor: 异步方法拦截器实例
        """
        self._detector = detector
        self._interceptor = interceptor

    def create_async_proxy(self, bean: Any, bean_name: str) -> Any:
        """创建异步代理对象

        Args:
            bean: 原始Bean对象
            bean_name: Bean名称

        Returns:
            Any: 代理后的Bean对象
        """
        try:
            # 扫描Bean中的异步方法
            async_methods = self._detector.scan_async_methods(bean)

            if not async_methods:
                return bean

            # 为异步方法创建代理
            for method_name, method_info in async_methods.items():
                original_method = getattr(bean, method_name)

                # 创建拦截的异步方法
                proxy_method = self._interceptor.create_intercepted_method(
                    original_method,
                    pool_name=method_info.get('pool'),
                    timeout=method_info.get('timeout')
                )

                # 替换原方法
                setattr(bean, method_name, proxy_method)

                logger.debug(f"Created async proxy for method: {bean_name}.{method_name}")

            return bean

        except Exception as e:
            logger.warning(f"Failed to create async proxy for {bean_name}: {e}")
            return bean


class AsyncAnnotationBeanPostProcessor(BeanPostProcessor):
    """异步注解Bean后置处理器（重构版）

    采用组合模式协调各个专门组件完成异步处理：
    - 使用 AsyncAnnotationDetector 检测异步注解
    - 使用 AsyncComponentInitializer 进行懒加载初始化
    - 使用 AsyncMethodInterceptor 处理方法拦截
    - 使用 AsyncBeanProxyCreator 创建代理对象

    符合单一职责原则，每个组件只负责一个明确的功能。
    """

    def __init__(self):
        """初始化异步注解处理器"""
        self._processed_beans: set[str] = set()

        # 组合各个专门的组件
        self._detector = AsyncAnnotationDetector()
        self._initializer = AsyncComponentInitializer()
        self._interceptor = AsyncMethodInterceptor(self._initializer)
        self._proxy_creator = AsyncBeanProxyCreator(self._detector, self._interceptor)

    def post_process_before_initialization(self, bean: Any, bean_name: str) -> Any:
        """在Bean初始化前处理"""
        return bean

    def post_process_after_initialization(self, bean: Any, bean_name: str) -> Any:
        """在Bean初始化后处理@Async注解（重构版）

        简化的主要处理逻辑，通过组合各个专门组件完成异步处理。
        """
        if bean is None or bean_name in self._processed_beans:
            return bean

        try:
            # 使用检测器检查是否有异步注解
            if not self._detector.has_async_annotation(bean):
                return bean

            # 使用初始化器进行懒加载初始化异步组件
            if not self._initializer.initialize_if_needed():
                return bean

            # 使用代理创建器创建异步代理
            self._processed_beans.add(bean_name)
            proxy_bean = self._proxy_creator.create_async_proxy(bean, bean_name)

            logger.info(f"✅ Processed async bean: {bean_name}")
            return proxy_bean

        except Exception as e:
            logger.error(f"Failed to process async annotations for bean '{bean_name}': {e}")
            return bean

    def get_order(self) -> int:
        """获取处理器执行顺序"""
        return ProcessorOrder.NORMAL.value


class AsyncLifecycleBeanPostProcessor(BeanPostProcessor):
    """异步生命周期Bean后置处理器

    负责管理异步组件的生命周期
    迁移自 asyncs.processor，现在位于正确的位置
    """

    def __init__(self):
        """初始化异步生命周期处理器"""
        self._lifecycle_managed = False

    def post_process_before_initialization(self, bean: Any, bean_name: str) -> Any:
        """在Bean初始化前处理"""
        return bean

    def post_process_after_initialization(self, bean: Any, bean_name: str) -> Any:
        """在Bean初始化后处理"""
        # 生命周期管理逻辑可以在这里添加
        return bean

    def get_order(self) -> int:
        """获取处理器执行顺序"""
        # 生命周期处理器应该最早执行
        return ProcessorOrder.HIGHEST.value


class AsyncBeanFactoryIntegration:
    """异步Bean工厂集成器

    负责将异步处理器集成到Bean工厂中
    迁移自 asyncs.processor，现在位于正确的位置
    """

    @staticmethod
    def register_async_processors(bean_factory) -> None:
        """注册异步处理器到Bean工厂

        Args:
            bean_factory: Bean工厂实例
        """
        try:
            # 检查Bean工厂是否支持后置处理器
            if not hasattr(bean_factory, "add_processor"):
                logger.warning("Bean factory does not support post processors")
                return

            # 注册异步生命周期处理器(优先级最高)
            lifecycle_processor = AsyncLifecycleBeanPostProcessor()
            bean_factory.add_processor(lifecycle_processor)

            # 注册异步注解处理器(优先级较低)
            annotation_processor = AsyncAnnotationBeanPostProcessor()
            bean_factory.add_processor(annotation_processor)

            logger.debug("Successfully registered async processors to Bean factory")

        except Exception as e:
            logger.error(f"Failed to register async processors: {e}")
            raise
