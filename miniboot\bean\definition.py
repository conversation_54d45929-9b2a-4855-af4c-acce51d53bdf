#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Bean定义系统
"""

from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Optional

from loguru import logger


class BeanScope(Enum):
    """核心Bean作用域枚举

    定义核心Bean的生命周期和实例化策略。
    只包含框架核心的作用域类型，Web相关作用域在web模块中定义。
    """

    SINGLETON = "singleton"  # 单例模式：容器中只有一个实例
    PROTOTYPE = "prototype"  # 原型模式：每次获取都创建新实例

    def __str__(self) -> str:
        return self.value

    def __repr__(self) -> str:
        return f"BeanScope.{self.name}"

    @classmethod
    def from_string(cls, value: str) -> "BeanScope":
        """从字符串创建BeanScope

        Args:
            value: 作用域字符串值

        Returns:
            BeanScope: 对应的作用域枚举

        Raises:
            ValueError: 如果值无效
        """
        value = value.lower().strip()
        for scope in cls:
            if scope.value == value:
                return scope
        raise ValueError(f"Invalid core scope value: {value}. Valid values: {[s.value for s in cls]}")

    def is_singleton(self) -> bool:
        """检查是否为单例作用域"""
        return self == BeanScope.SINGLETON

    def is_prototype(self) -> bool:
        """检查是否为原型作用域"""
        return self == BeanScope.PROTOTYPE

    @classmethod
    def is_core_scope(cls, value: str) -> bool:
        """检查是否为核心作用域

        Args:
            value: 作用域字符串值

        Returns:
            bool: 如果是核心作用域返回True
        """
        try:
            cls.from_string(value)
            return True
        except ValueError:
            return False


@dataclass
class PropertyValue:
    """Bean属性值定义

    用于描述Bean的属性注入信息。
    支持直接值注入和Bean引用注入。
    """

    name: str  # 属性名称
    value: Any = None  # 属性值（直接值）
    ref: Optional[str] = None  # Bean引用名称
    type_hint: Optional[type] = None  # 类型提示

    def __post_init__(self):
        """初始化后处理"""
        if not self.name:
            logger.error("Property definition validation failed: Property name cannot be empty")
            raise ValueError("Property name cannot be empty")

        if self.value is None and self.ref is None:
            logger.error(f"Property definition validation failed: Property '{self.name}' must have either value or ref")
            raise ValueError("Either value or ref must be specified")

        if self.value is not None and self.ref is not None:
            logger.error(f"Property definition validation failed: Property '{self.name}' cannot specify both value and ref")
            raise ValueError("Cannot specify both value and ref")

    def is_reference(self) -> bool:
        """检查是否为Bean引用

        Returns:
            bool: 如果是Bean引用返回True，否则返回False
        """
        return self.ref is not None

    def get_resolved_value(self) -> Any:
        """获取解析后的值

        Returns:
            Any: 如果是直接值返回value，如果是引用返回ref
        """
        return self.ref if self.is_reference() else self.value


@dataclass
class ConstructorArgument:
    """构造函数参数定义

    用于描述Bean构造函数的参数信息。
    支持按索引和按名称的参数注入。
    """

    index: int  # 参数索引
    value: Any = None  # 参数值（直接值）
    ref: Optional[str] = None  # Bean引用名称
    type_hint: Optional[type] = None  # 类型提示
    name: Optional[str] = None  # 参数名称（可选）

    def __post_init__(self):
        """初始化后处理"""
        if self.index < 0:
            raise ValueError("Constructor argument index must be non-negative")

        if self.value is None and self.ref is None:
            raise ValueError("Either value or ref must be specified")

        if self.value is not None and self.ref is not None:
            raise ValueError("Cannot specify both value and ref")

    def is_reference(self) -> bool:
        """检查是否为Bean引用

        Returns:
            bool: 如果是Bean引用返回True，否则返回False
        """
        return self.ref is not None

    def get_resolved_value(self) -> Any:
        """获取解析后的值

        Returns:
            Any: 如果是直接值返回value，如果是引用返回ref
        """
        return self.ref if self.is_reference() else self.value


@dataclass
class BeanDefinition:
    """Bean定义类

    包含Bean的完整定义信息，包括类型、作用域、生命周期方法、
    依赖关系、属性值等所有必要的元数据。
    """

    # 基本信息
    bean_name: str  # Bean名称，容器中的唯一标识
    bean_class: type  # Bean类型
    scope: BeanScope = BeanScope.SINGLETON  # Bean作用域，默认单例

    # 实例化相关
    factory_method_name: Optional[str] = None  # 工厂方法名称
    factory_bean_name: Optional[str] = None  # 工厂Bean名称
    constructor_args: list[ConstructorArgument] = field(default_factory=list)  # 构造函数参数

    # 属性注入
    property_values: list[PropertyValue] = field(default_factory=list)  # 属性值列表

    # 生命周期方法
    init_method_name: Optional[str] = None  # 初始化方法名称
    destroy_method_name: Optional[str] = None  # 销毁方法名称

    # 配置选项
    lazy_init: bool = False  # 是否延迟初始化
    primary: bool = False  # 是否为主要Bean
    depends_on: list[str] = field(default_factory=list)  # 依赖的Bean名称列表

    # 元数据
    description: Optional[str] = None  # Bean描述信息
    source: Optional[str] = None  # Bean定义来源

    def __post_init__(self):
        """初始化后处理"""
        if not self.bean_name:
            logger.error("Bean definition validation failed: Bean name cannot be empty")
            raise ValueError("Bean name cannot be empty")
        if not self.bean_class:
            logger.error("Bean definition validation failed: Bean class cannot be None")
            raise ValueError("Bean class cannot be None")

        logger.debug(f"Bean definition created: {self.bean_name} -> {self.bean_class.__name__}")
        if not isinstance(self.scope, BeanScope):
            raise ValueError(f"Scope must be a BeanScope enum, got {type(self.scope)}")

    def add_property(self, name: str, value: Any = None, ref: Optional[str] = None, type_hint: Optional[type] = None) -> "BeanDefinition":
        """添加属性值

        Args:
            name: 属性名称
            value: 属性值（与ref互斥）
            ref: Bean引用名称（与value互斥）
            type_hint: 类型提示

        Returns:
            BeanDefinition: 返回自身支持链式调用
        """
        property_value = PropertyValue(name=name, value=value, ref=ref, type_hint=type_hint)
        self.property_values.append(property_value)
        return self

    def add_arg(
        self, index: int, value: Any = None, ref: Optional[str] = None, type_hint: Optional[type] = None, name: Optional[str] = None
    ) -> "BeanDefinition":
        """添加构造函数参数

        Args:
            index: 参数索引
            value: 参数值（与ref互斥）
            ref: Bean引用名称（与value互斥）
            type_hint: 类型提示
            name: 参数名称

        Returns:
            BeanDefinition: 返回自身支持链式调用
        """
        constructor_arg = ConstructorArgument(index=index, value=value, ref=ref, type_hint=type_hint, name=name)
        self.constructor_args.append(constructor_arg)
        # 按索引排序
        self.constructor_args.sort(key=lambda arg: arg.index)
        return self

    def property(self, name: str) -> Optional[PropertyValue]:
        """获取指定名称的属性值

        Args:
            name: 属性名称

        Returns:
            Optional[PropertyValue]: 属性值对象，如果不存在返回None
        """
        for prop in self.property_values:
            if prop.name == name:
                return prop
        return None

    def has_property(self, name: str) -> bool:
        """检查是否包含指定属性值

        Args:
            name: 属性名称

        Returns:
            bool: 如果包含返回True
        """
        return self.property(name) is not None

    def remove_property(self, name: str) -> bool:
        """移除指定属性值

        Args:
            name: 属性名称

        Returns:
            bool: 如果成功移除返回True
        """
        for i, prop in enumerate(self.property_values):
            if prop.name == name:
                del self.property_values[i]
                return True
        return False

    def arg(self, index: int) -> Optional[ConstructorArgument]:
        """获取指定索引的构造函数参数

        Args:
            index: 参数索引

        Returns:
            Optional[ConstructorArgument]: 构造函数参数对象，如果不存在返回None
        """
        for arg in self.constructor_args:
            if arg.index == index:
                return arg
        return None

    def has_args(self) -> bool:
        """检查是否有构造函数参数

        Returns:
            bool: 如果有构造函数参数返回True
        """
        return len(self.constructor_args) > 0

    def singleton(self) -> bool:
        """检查是否为单例Bean

        Returns:
            bool: 如果是单例返回True
        """
        return self.scope.is_singleton()

    def prototype(self) -> bool:
        """检查是否为原型Bean

        Returns:
            bool: 如果是原型返回True
        """
        return self.scope.is_prototype()

    def has_init(self) -> bool:
        """检查是否有初始化方法

        Returns:
            bool: 如果有初始化方法返回True
        """
        return self.init_method_name is not None

    def has_destroy(self) -> bool:
        """检查是否有销毁方法

        Returns:
            bool: 如果有销毁方法返回True
        """
        return self.destroy_method_name is not None

    def lazy(self) -> bool:
        """检查是否延迟初始化

        Returns:
            bool: 如果延迟初始化返回True
        """
        return self.lazy_init

    def is_primary(self) -> bool:
        """检查是否为主要Bean

        Returns:
            bool: 如果是主要Bean返回True
        """
        return self.primary

    def has_dependencies(self) -> bool:
        """检查是否有依赖

        Returns:
            bool: 如果有依赖返回True
        """
        return len(self.depends_on) > 0

    def add_dependency(self, bean_name: str) -> None:
        """添加依赖Bean

        Args:
            bean_name: 依赖的Bean名称
        """
        if bean_name and bean_name not in self.depends_on:
            self.depends_on.append(bean_name)

    def remove_dependency(self, bean_name: str) -> bool:
        """移除依赖Bean

        Args:
            bean_name: 依赖的Bean名称

        Returns:
            bool: 如果成功移除返回True
        """
        if bean_name in self.depends_on:
            self.depends_on.remove(bean_name)
            return True
        return False

    def __str__(self) -> str:
        """字符串表示"""
        return f"BeanDefinition(name='{self.bean_name}', class={self.bean_class.__name__}, scope={self.scope})"

    def __repr__(self) -> str:
        """详细字符串表示"""
        return (
            f"BeanDefinition(bean_name='{self.bean_name}', "
            f"bean_class={self.bean_class}, scope={self.scope}, "
            f"lazy_init={self.lazy_init}, primary={self.primary})"
        )
