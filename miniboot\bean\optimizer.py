#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Bean创建优化器 - 优化Bean创建过程的性能和效率
"""

import threading
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
from typing import Any, Callable, Optional, Union

from .definition import BeanDefinition, BeanScope


class CreationStrategy(Enum):
    """Bean创建策略"""

    DIRECT = "direct"  # 直接创建
    CACHED = "cached"  # 缓存创建
    POOLED = "pooled"  # 池化创建
    LAZY = "lazy"  # 延迟创建
    BATCH = "batch"  # 批量创建


@dataclass
class CreationMetrics:
    """Bean创建指标"""

    bean_name: str
    creation_count: int = 0
    total_creation_time: float = 0.0
    average_creation_time: float = 0.0
    cache_hits: int = 0
    cache_misses: int = 0
    strategy_used: CreationStrategy = CreationStrategy.DIRECT
    last_creation_time: float = 0.0

    def update_creation_time(self, creation_time: float) -> None:
        """更新创建时间统计"""
        self.creation_count += 1
        self.total_creation_time += creation_time
        self.average_creation_time = self.total_creation_time / self.creation_count
        self.last_creation_time = creation_time

    def record_cache_hit(self) -> None:
        """记录缓存命中"""
        self.cache_hits += 1

    def record_cache_miss(self) -> None:
        """记录缓存未命中"""
        self.cache_misses += 1

    @property
    def cache_hit_rate(self) -> float:
        """缓存命中率"""
        total = self.cache_hits + self.cache_misses
        return self.cache_hits / total if total > 0 else 0.0


@dataclass
class CreationConfig:
    """Bean创建配置"""

    enable_caching: bool = True
    enable_pooling: bool = True
    enable_batch_creation: bool = True
    enable_pre_creation: bool = True

    # 缓存配置
    cache_size: int = 1000
    cache_ttl: int = 3600  # 缓存生存时间（秒）

    # 池化配置
    pool_size: int = 10
    pool_max_idle_time: int = 300  # 池中对象最大空闲时间（秒）

    # 批量创建配置
    batch_size: int = 50
    batch_timeout: float = 0.1  # 批量创建超时时间（秒）

    # 预创建配置
    pre_creation_threshold: int = 5  # 使用次数超过此值时预创建
    pre_creation_count: int = 3  # 预创建数量


class BeanCreationStrategy(ABC):
    """Bean创建策略接口"""

    @abstractmethod
    def create_bean(self, bean_definition: BeanDefinition, *args, **kwargs) -> Any:
        """创建Bean实例"""
        pass

    @abstractmethod
    def can_handle(self, bean_definition: BeanDefinition) -> bool:
        """判断是否可以处理此Bean定义"""
        pass

    @abstractmethod
    def get_strategy_type(self) -> CreationStrategy:
        """获取策略类型"""
        pass


class DirectCreationStrategy(BeanCreationStrategy):
    """直接创建策略"""

    def create_bean(self, bean_definition: BeanDefinition, *args, **kwargs) -> Any:
        """直接创建Bean实例"""
        return bean_definition.bean_class(*args, **kwargs)

    def can_handle(self, _bean_definition: BeanDefinition) -> bool:
        """所有Bean都可以使用直接创建"""
        return True

    def get_strategy_type(self) -> CreationStrategy:
        return CreationStrategy.DIRECT


class CachedCreationStrategy(BeanCreationStrategy):
    """缓存创建策略"""

    def __init__(self, config: CreationConfig):
        self.config = config
        self._cache: dict[str, Any] = {}
        self._cache_timestamps: dict[str, float] = {}
        self._lock = threading.RLock()

    def create_bean(self, bean_definition: BeanDefinition, *args, **kwargs) -> Any:
        """从缓存创建或创建新实例"""
        cache_key = self._get_cache_key(bean_definition, args, kwargs)

        with self._lock:
            # 检查缓存
            if cache_key in self._cache:
                # 检查缓存是否过期
                if not self._is_cache_expired(cache_key):
                    return self._cache[cache_key]
                else:
                    # 清理过期缓存
                    self._remove_from_cache(cache_key)

            # 创建新实例并缓存
            instance = bean_definition.bean_class(*args, **kwargs)
            self._add_to_cache(cache_key, instance)
            return instance

    def can_handle(self, bean_definition: BeanDefinition) -> bool:
        """单例Bean适合缓存创建"""
        return bean_definition.scope == BeanScope.SINGLETON

    def get_strategy_type(self) -> CreationStrategy:
        return CreationStrategy.CACHED

    def _get_cache_key(self, bean_definition: BeanDefinition, args: tuple, kwargs: dict) -> str:
        """生成缓存键"""
        args_str = str(args) if args else ""
        kwargs_str = str(sorted(kwargs.items())) if kwargs else ""
        return f"{bean_definition.bean_name}:{args_str}:{kwargs_str}"

    def _is_cache_expired(self, cache_key: str) -> bool:
        """检查缓存是否过期"""
        if cache_key not in self._cache_timestamps:
            return True

        age = time.time() - self._cache_timestamps[cache_key]
        return age > self.config.cache_ttl

    def _add_to_cache(self, cache_key: str, instance: Any) -> None:
        """添加到缓存"""
        # 检查缓存大小限制
        if len(self._cache) >= self.config.cache_size:
            self._evict_oldest()

        self._cache[cache_key] = instance
        self._cache_timestamps[cache_key] = time.time()

    def _remove_from_cache(self, cache_key: str) -> None:
        """从缓存中移除"""
        self._cache.pop(cache_key, None)
        self._cache_timestamps.pop(cache_key, None)

    def _evict_oldest(self) -> None:
        """驱逐最旧的缓存项"""
        if not self._cache_timestamps:
            return

        oldest_key = min(self._cache_timestamps.keys(), key=lambda k: self._cache_timestamps[k])
        self._remove_from_cache(oldest_key)


class PooledCreationStrategy(BeanCreationStrategy):
    """池化创建策略"""

    def __init__(self, config: CreationConfig):
        self.config = config
        self._pools: dict[str, list[Any]] = {}
        self._pool_timestamps: dict[str, list[float]] = {}
        self._lock = threading.RLock()

    def create_bean(self, bean_definition: BeanDefinition, *args, **kwargs) -> Any:
        """从池中获取或创建新实例"""
        pool_key = bean_definition.bean_name

        with self._lock:
            # 尝试从池中获取
            if pool_key in self._pools and self._pools[pool_key]:
                # 清理过期对象
                self._cleanup_expired_objects(pool_key)

                if self._pools[pool_key]:
                    instance = self._pools[pool_key].pop(0)
                    self._pool_timestamps[pool_key].pop(0)
                    return instance

            # 池中没有可用对象，创建新实例
            return bean_definition.bean_class(*args, **kwargs)

    def can_handle(self, bean_definition: BeanDefinition) -> bool:
        """原型Bean适合池化创建"""
        return bean_definition.scope == BeanScope.PROTOTYPE

    def get_strategy_type(self) -> CreationStrategy:
        return CreationStrategy.POOLED

    def return_to_pool(self, bean_name: str, instance: Any) -> None:
        """将实例返回到池中"""
        with self._lock:
            if bean_name not in self._pools:
                self._pools[bean_name] = []
                self._pool_timestamps[bean_name] = []

            # 检查池大小限制
            if len(self._pools[bean_name]) < self.config.pool_size:
                self._pools[bean_name].append(instance)
                self._pool_timestamps[bean_name].append(time.time())

    def _cleanup_expired_objects(self, pool_key: str) -> None:
        """清理过期对象"""
        if pool_key not in self._pool_timestamps:
            return

        current_time = time.time()
        valid_indices = []

        for i, timestamp in enumerate(self._pool_timestamps[pool_key]):
            if current_time - timestamp <= self.config.pool_max_idle_time:
                valid_indices.append(i)

        # 保留有效对象
        if valid_indices:
            self._pools[pool_key] = [self._pools[pool_key][i] for i in valid_indices]
            self._pool_timestamps[pool_key] = [self._pool_timestamps[pool_key][i] for i in valid_indices]
        else:
            self._pools[pool_key] = []
            self._pool_timestamps[pool_key] = []


class BeanCreationOptimizer:
    """Bean创建优化器"""

    def __init__(self, config: Optional[CreationConfig] = None):
        self.config = config or CreationConfig()

        # 创建策略
        self._strategies: list[BeanCreationStrategy] = [
            CachedCreationStrategy(self.config),
            PooledCreationStrategy(self.config),
            DirectCreationStrategy(),  # 默认策略，放在最后
        ]

        # 性能指标
        self._metrics: dict[str, CreationMetrics] = {}
        self._lock = threading.RLock()

        # 批量创建队列
        self._batch_queue: dict[str, list[tuple]] = {}
        self._batch_timers: dict[str, threading.Timer] = {}

        # 预创建管理
        self._pre_creation_candidates: set[str] = set()

    def create_bean(self, bean_definition: BeanDefinition, *args, **kwargs) -> Any:
        """优化的Bean创建"""
        start_time = time.time()

        try:
            # 选择最优创建策略
            strategy = self._select_strategy(bean_definition)

            # 执行创建
            instance = strategy.create_bean(bean_definition, *args, **kwargs)

            # 更新指标
            creation_time = time.time() - start_time
            self._update_metrics(bean_definition.bean_name, creation_time, strategy.get_strategy_type())

            # 检查是否需要预创建
            self._check_pre_creation(bean_definition)

            return instance

        except Exception as e:
            creation_time = time.time() - start_time
            self._update_metrics(bean_definition.bean_name, creation_time, CreationStrategy.DIRECT, failed=True)
            raise e

    def _select_strategy(self, bean_definition: BeanDefinition) -> BeanCreationStrategy:
        """选择最优创建策略"""
        for strategy in self._strategies:
            if strategy.can_handle(bean_definition):
                return strategy

        # 默认使用直接创建策略
        return self._strategies[-1]

    def _update_metrics(self, bean_name: str, creation_time: float, strategy: CreationStrategy, failed: bool = False) -> None:
        """更新创建指标"""
        with self._lock:
            if bean_name not in self._metrics:
                self._metrics[bean_name] = CreationMetrics(bean_name)

            metrics = self._metrics[bean_name]
            if not failed:
                metrics.update_creation_time(creation_time)
                metrics.strategy_used = strategy

    def _check_pre_creation(self, bean_definition: BeanDefinition) -> None:
        """检查是否需要预创建"""
        if not self.config.enable_pre_creation:
            return

        bean_name = bean_definition.bean_name
        metrics = self._metrics.get(bean_name)

        if metrics and metrics.creation_count >= self.config.pre_creation_threshold and bean_name not in self._pre_creation_candidates:
            self._pre_creation_candidates.add(bean_name)
            # 这里可以触发预创建逻辑

    def get_metrics(self, bean_name: Optional[str] = None) -> Union[CreationMetrics, dict[str, CreationMetrics]]:
        """获取创建指标"""
        with self._lock:
            if bean_name:
                return self._metrics.get(bean_name)
            return self._metrics.copy()

    def get_performance_summary(self) -> dict[str, Any]:
        """获取性能摘要"""
        with self._lock:
            total_creations = sum(m.creation_count for m in self._metrics.values())
            total_time = sum(m.total_creation_time for m in self._metrics.values())

            strategy_counts = {}
            for metrics in self._metrics.values():
                strategy = metrics.strategy_used.value
                strategy_counts[strategy] = strategy_counts.get(strategy, 0) + metrics.creation_count

            return {
                "total_beans": len(self._metrics),
                "total_creations": total_creations,
                "total_creation_time": total_time,
                "average_creation_time": total_time / total_creations if total_creations > 0 else 0.0,
                "strategy_distribution": strategy_counts,
                "pre_creation_candidates": len(self._pre_creation_candidates),
            }

    def clear_metrics(self) -> None:
        """清理指标"""
        with self._lock:
            self._metrics.clear()
            self._pre_creation_candidates.clear()


class BatchCreationManager:
    """批量创建管理器"""

    def __init__(self, config: CreationConfig):
        self.config = config
        self._batch_queue: dict[str, list[tuple]] = {}
        self._batch_timers: dict[str, threading.Timer] = {}
        self._lock = threading.RLock()
        self._results: dict[str, list[Any]] = {}

    def add_to_batch(self, bean_definition: BeanDefinition, callback: Callable[[Any], None], *args, **kwargs) -> None:
        """添加到批量创建队列"""
        if not self.config.enable_batch_creation:
            # 如果未启用批量创建，直接创建
            instance = bean_definition.bean_class(*args, **kwargs)
            callback(instance)
            return

        batch_key = bean_definition.bean_name

        with self._lock:
            if batch_key not in self._batch_queue:
                self._batch_queue[batch_key] = []
                self._results[batch_key] = []

            # 添加到队列
            self._batch_queue[batch_key].append((bean_definition, callback, args, kwargs))

            # 检查是否达到批量大小或设置定时器
            if len(self._batch_queue[batch_key]) >= self.config.batch_size:
                self._process_batch(batch_key)
            elif batch_key not in self._batch_timers:
                # 设置定时器
                timer = threading.Timer(self.config.batch_timeout, self._process_batch, [batch_key])
                self._batch_timers[batch_key] = timer
                timer.start()

    def _process_batch(self, batch_key: str) -> None:
        """处理批量创建"""
        with self._lock:
            if batch_key not in self._batch_queue or not self._batch_queue[batch_key]:
                return

            # 取消定时器
            if batch_key in self._batch_timers:
                self._batch_timers[batch_key].cancel()
                del self._batch_timers[batch_key]

            # 获取批量任务
            batch_tasks = self._batch_queue[batch_key]
            self._batch_queue[batch_key] = []

            # 批量创建
            for bean_definition, callback, args, kwargs in batch_tasks:
                try:
                    instance = bean_definition.bean_class(*args, **kwargs)
                    callback(instance)
                except Exception:
                    # 处理创建失败
                    callback(None)


class PreCreationManager:
    """预创建管理器"""

    def __init__(self, config: CreationConfig):
        self.config = config
        self._pre_created_instances: dict[str, list[Any]] = {}
        self._creation_counts: dict[str, int] = {}
        self._lock = threading.RLock()

    def record_creation(self, bean_name: str) -> None:
        """记录Bean创建"""
        with self._lock:
            self._creation_counts[bean_name] = self._creation_counts.get(bean_name, 0) + 1

            # 检查是否需要预创建
            if self._creation_counts[bean_name] >= self.config.pre_creation_threshold and bean_name not in self._pre_created_instances:
                self._trigger_pre_creation(bean_name)

    def get_pre_created_instance(self, bean_name: str) -> Optional[Any]:
        """获取预创建的实例"""
        with self._lock:
            if bean_name in self._pre_created_instances and self._pre_created_instances[bean_name]:
                return self._pre_created_instances[bean_name].pop(0)
            return None

    def _trigger_pre_creation(self, bean_name: str) -> None:
        """触发预创建"""
        # 这里应该与Bean工厂集成，预创建指定数量的实例
        # 暂时使用占位符实现
        self._pre_created_instances[bean_name] = []

    def add_pre_created_instance(self, bean_name: str, instance: Any) -> None:
        """添加预创建的实例"""
        with self._lock:
            if bean_name not in self._pre_created_instances:
                self._pre_created_instances[bean_name] = []

            if len(self._pre_created_instances[bean_name]) < self.config.pre_creation_count:
                self._pre_created_instances[bean_name].append(instance)

    def get_statistics(self) -> dict[str, Any]:
        """获取预创建统计信息"""
        with self._lock:
            return {
                "pre_created_beans": len(self._pre_created_instances),
                "total_pre_created_instances": sum(len(instances) for instances in self._pre_created_instances.values()),
                "creation_counts": self._creation_counts.copy(),
                "pre_creation_candidates": [name for name, count in self._creation_counts.items() if count >= self.config.pre_creation_threshold],
            }


class CreationPerformanceMonitor:
    """创建性能监控器"""

    def __init__(self):
        self._performance_data: dict[str, list[float]] = {}
        self._lock = threading.RLock()
        self._alerts: list[dict[str, Any]] = []

    def record_creation_time(self, bean_name: str, creation_time: float) -> None:
        """记录创建时间"""
        with self._lock:
            if bean_name not in self._performance_data:
                self._performance_data[bean_name] = []

            self._performance_data[bean_name].append(creation_time)

            # 检查性能异常
            self._check_performance_anomaly(bean_name, creation_time)

    def _check_performance_anomaly(self, bean_name: str, creation_time: float) -> None:
        """检查性能异常"""
        data = self._performance_data[bean_name]

        if len(data) < 5:  # 需要足够的数据点
            return

        # 计算平均值和标准差
        avg_time = sum(data) / len(data)
        variance = sum((t - avg_time) ** 2 for t in data) / len(data)
        std_dev = variance**0.5

        # 如果创建时间超过平均值+2倍标准差，记录异常
        if creation_time > avg_time + 2 * std_dev:
            alert = {
                "bean_name": bean_name,
                "creation_time": creation_time,
                "average_time": avg_time,
                "threshold": avg_time + 2 * std_dev,
                "timestamp": time.time(),
                "type": "slow_creation",
            }
            self._alerts.append(alert)

    def get_performance_report(self) -> dict[str, Any]:
        """获取性能报告"""
        with self._lock:
            report = {"monitored_beans": len(self._performance_data), "total_alerts": len(self._alerts), "bean_performance": {}}

            for bean_name, times in self._performance_data.items():
                if times:
                    report["bean_performance"][bean_name] = {
                        "count": len(times),
                        "min_time": min(times),
                        "max_time": max(times),
                        "avg_time": sum(times) / len(times),
                        "latest_time": times[-1],
                    }

            return report

    def get_alerts(self, limit: int = 10) -> list[dict[str, Any]]:
        """获取最近的性能警报"""
        with self._lock:
            return self._alerts[-limit:] if self._alerts else []

    def clear_alerts(self) -> None:
        """清理警报"""
        with self._lock:
            self._alerts.clear()


def create_bean_creation_optimizer(config: Optional[CreationConfig] = None) -> BeanCreationOptimizer:
    """创建Bean创建优化器"""
    return BeanCreationOptimizer(config)


def create_creation_config(**kwargs) -> CreationConfig:
    """创建创建配置"""
    return CreationConfig(**kwargs)


class OptimizedBeanFactory:
    """优化的Bean工厂 - 集成创建优化功能"""

    def __init__(self, base_factory: Any, config: Optional[CreationConfig] = None):
        self._base_factory = base_factory
        self.config = config or CreationConfig()

        # 创建优化组件
        self._optimizer = create_bean_creation_optimizer(self.config)
        self._batch_manager = BatchCreationManager(self.config)
        self._pre_creation_manager = PreCreationManager(self.config)
        self._performance_monitor = CreationPerformanceMonitor()

        # 线程安全
        self._lock = threading.RLock()

        # 统计信息
        self._creation_stats = {
            "total_creations": 0,
            "optimized_creations": 0,
            "batch_creations": 0,
            "pre_created_hits": 0,
            "cache_hits": 0,
        }

    def get_bean(self, bean_name: str, *args, **kwargs) -> Any:
        """获取Bean实例（优化版本）"""
        start_time = time.time()

        try:
            # 尝试从预创建实例获取
            pre_created = self._pre_creation_manager.get_pre_created_instance(bean_name)
            if pre_created is not None:
                self._creation_stats["pre_created_hits"] += 1
                return pre_created

            # 获取Bean定义
            if hasattr(self._base_factory, "get_bean_definition"):
                bean_definition = self._base_factory.get_bean_definition(bean_name)
                if not bean_definition:
                    raise ValueError(f"No bean definition found for '{bean_name}'")

                # 使用优化器创建Bean
                instance = self._optimizer.create_bean(bean_definition, *args, **kwargs)

                # 记录创建
                self._pre_creation_manager.record_creation(bean_name)

                # 更新统计
                creation_time = time.time() - start_time
                self._performance_monitor.record_creation_time(bean_name, creation_time)
                self._creation_stats["total_creations"] += 1
                self._creation_stats["optimized_creations"] += 1

                return instance
            else:
                # 回退到基础工厂
                return self._base_factory.get_bean(bean_name, *args, **kwargs)

        except Exception:
            # 回退到基础工厂
            return self._base_factory.get_bean(bean_name, *args, **kwargs)

    def create_bean_batch(self, bean_requests: list[dict[str, Any]]) -> list[Any]:
        """批量创建Bean"""
        if not self.config.enable_batch_creation:
            # 如果未启用批量创建，逐个创建
            return [self.get_bean(**request) for request in bean_requests]

        results = []
        result_lock = threading.Lock()

        def callback(instance: Any) -> None:
            with result_lock:
                results.append(instance)

        # 添加到批量队列
        for request in bean_requests:
            bean_name = request.get("bean_name")
            if bean_name:
                self._batch_manager.add_to_batch(bean_name, callback)

        # 等待批量处理完成
        self._batch_manager.process_batch()

        self._creation_stats["batch_creations"] += len(results)
        return results

    def get_creation_stats(self) -> dict[str, Any]:
        """获取创建统计信息"""
        with self._lock:
            return {
                "stats": self._creation_stats.copy(),
                "optimizer_metrics": self._optimizer.get_metrics(),
                "performance_summary": self._performance_monitor.get_summary(),
                "batch_info": self._batch_manager.get_batch_info(),
                "pre_creation_info": self._pre_creation_manager.get_pre_creation_info(),
            }

    def optimize_for_bean(self, bean_name: str) -> None:
        """为特定Bean启用优化"""
        self._pre_creation_manager.enable_pre_creation(bean_name)

    def clear_cache(self) -> None:
        """清理缓存"""
        self._optimizer.clear_cache()
        self._pre_creation_manager.clear_cache()

    def shutdown(self) -> None:
        """关闭优化工厂"""
        self._batch_manager.shutdown()
        self._pre_creation_manager.shutdown()
        self._performance_monitor.shutdown()


# 便捷函数已删除 - 请直接使用类构造器
# 使用方式：
# factory = OptimizedBeanFactory(base_factory, config)
# config = CreationConfig(enable_caching=True, enable_pooling=True, ...)
# stats = factory.get_creation_stats()
