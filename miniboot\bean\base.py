#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Bean模块分层接口定义 - 重构后的分层架构
"""

from abc import ABC, abstractmethod
from typing import Any, Optional, TypeVar


T = TypeVar("T")


class BeanFactory(ABC):
    """Bean工厂基础接口 - 内置异步适配

    定义Bean工厂的核心功能,自动适配同步/异步执行环境。
    这是所有Bean工厂的根接口,提供最基本的Bean获取功能。
    """

    @abstractmethod
    def get_bean(self, name: str, required_type: Optional[type] = None) -> Any:
        """获取Bean实例 - 自动适配同步/异步执行环境

        根据Bean名称获取Bean实例框架自动检测执行环境并选择最优方式。
        在异步环境中返回awaitable对象在同步环境中直接返回Bean实例。

        Args:
            name: Bean名称，必须是已注册的Bean名称
            required_type: 期望的Bean类型，用于类型检查（可选）

        Returns:
            Bean实例或awaitable对象，具体类型取决于Bean定义和执行环境

        Raises:
            NoSuchBeanDefinitionError: 如果Bean不存在
            BeanCreationError: 如果Bean创建失败
            TypeError: 如果Bean类型不匹配required_type
        """
        pass

    @abstractmethod
    def contains_bean(self, name: str) -> bool:
        """检查Bean是否存在

        检查指定名称的Bean是否已注册并可用。

        Args:
            name: Bean名称

        Returns:
            bool: 如果Bean存在返回True，否则返回False
        """
        pass

    @abstractmethod
    def is_singleton(self, name: str) -> bool:
        """检查Bean是否为单例

        Args:
            name: Bean名称

        Returns:
            bool: 如果Bean是单例返回True，否则返回False

        Raises:
            NoSuchBeanDefinitionError: 如果Bean不存在
        """
        pass

    @abstractmethod
    def get_type(self, name: str) -> Optional[type]:
        """获取Bean类型

        Args:
            name: Bean名称

        Returns:
            Bean的类型，如果Bean不存在返回None
        """
        pass

    def get(self, name: str, required_type: Optional[type] = None) -> Any:
        """获取Bean实例 - 兼容性方法

        这是get_bean方法的别名，用于向后兼容。

        Args:
            name: Bean名称
            required_type: 期望的Bean类型

        Returns:
            Bean实例或awaitable对象
        """
        return self.get_bean(name, required_type)

    def contains(self, name: str) -> bool:
        """检查Bean是否存在 - 兼容性方法

        这是contains_bean方法的别名，用于向后兼容。

        Args:
            name: Bean名称

        Returns:
            bool: 如果Bean存在返回True，否则返回False
        """
        return self.contains_bean(name)

    def get_bean_names(self) -> list[str]:
        """获取所有Bean名称

        返回当前工厂中所有已注册的Bean名称列表。

        Returns:
            List[str]: Bean名称列表，如果没有Bean则返回空列表
        """
        return []

    def names(self) -> list[str]:
        """获取所有Bean名称 - 兼容性方法

        这是get_bean_names方法的别名，用于向后兼容。

        Returns:
            list[str]: Bean名称列表，如果没有Bean则返回空列表
        """
        return self.get_bean_names()

    def singleton(self, name: str) -> bool:
        """检查Bean是否为单例 - 兼容性方法

        这是is_singleton方法的别名，用于向后兼容。

        Args:
            name: Bean名称

        Returns:
            bool: 如果Bean是单例返回True，否则返回False

        Raises:
            NoSuchBeanDefinitionError: 如果Bean不存在
        """
        return self.is_singleton(name)

    def type(self, name: str) -> Optional[type]:
        """获取Bean的类型 - 兼容性方法

        这是get_type方法的别名，用于向后兼容。

        Args:
            name: Bean名称

        Returns:
            Optional[type]: Bean的类型，如果无法确定则返回None

        Raises:
            NoSuchBeanDefinitionError: 如果Bean不存在
        """
        return self.get_type(name)


class HierarchicalBeanFactory(BeanFactory):
    """分层Bean工厂接口

    支持父子工厂层次结构，子工厂可以从父工厂继承Bean定义。
    这允许创建模块化的Bean工厂结构，支持Bean的分层管理。
    """

    @abstractmethod
    def get_parent_bean_factory(self) -> Optional["BeanFactory"]:
        """获取父Bean工厂

        Returns:
            父Bean工厂实例，如果没有父工厂返回None
        """
        pass

    @abstractmethod
    def contains_local_bean(self, name: str) -> bool:
        """检查本地Bean是否存在（不包括父工厂）

        Args:
            name: Bean名称

        Returns:
            bool: 如果本地Bean存在返回True，否则返回False
        """
        pass


class ConfigurableBeanFactory(HierarchicalBeanFactory):
    """可配置Bean工厂接口

    扩展分层Bean工厂，添加配置管理功能。
    支持Bean作用域注册、后置处理器管理等高级功能。
    """

    @abstractmethod
    def set_parent_bean_factory(self, parent_bean_factory: Optional[BeanFactory]) -> None:
        """设置父Bean工厂

        Args:
            parent_bean_factory: 父Bean工厂实例，可以为None
        """
        pass

    @abstractmethod
    def register_scope(self, scope_name: str, scope: "Scope") -> None:
        """注册Bean作用域

        Args:
            scope_name: 作用域名称
            scope: 作用域实现
        """
        pass

    @abstractmethod
    def add_bean_post_processor(self, bean_post_processor: "BeanPostProcessor") -> None:
        """添加Bean后置处理器

        Args:
            bean_post_processor: Bean后置处理器实例
        """
        pass

    @abstractmethod
    def get_bean_post_processor_count(self) -> int:
        """获取Bean后置处理器数量

        Returns:
            int: 后置处理器的数量
        """
        pass

    @abstractmethod
    def destroy_singletons(self) -> None:
        """销毁所有单例Bean

        释放所有单例Bean的资源，调用其销毁方法。
        """
        pass


class ListableBeanFactory(BeanFactory):
    """可列举Bean工厂接口

    扩展Bean工厂，添加Bean枚举和批量操作功能。
    支持按类型查找Bean、获取Bean定义等高级查询功能。
    """

    @abstractmethod
    def get_beans_of_type(self, bean_type: type[T], include_non_singletons: bool = True, allow_eager_init: bool = True) -> dict[str, T]:
        """获取指定类型的所有Bean

        Args:
            bean_type: Bean类型
            include_non_singletons: 是否包含非单例Bean
            allow_eager_init: 是否允许提前初始化

        Returns:
            Dict[str, T]: Bean名称到Bean实例的映射
        """
        pass

    @abstractmethod
    def get_bean_names_for_type(self, bean_type: type, include_non_singletons: bool = True, allow_eager_init: bool = True) -> list[str]:
        """获取指定类型的所有Bean名称

        Args:
            bean_type: Bean类型
            include_non_singletons: 是否包含非单例Bean
            allow_eager_init: 是否允许提前初始化

        Returns:
            List[str]: Bean名称列表
        """
        pass


class BeanDefinitionRegistry(ABC):
    """Bean定义注册表接口

    管理Bean定义的注册、获取和查询功能。
    Bean定义包含了创建Bean实例所需的所有信息。
    """

    @abstractmethod
    def register(self, name: str, bean_definition: "BeanDefinition") -> None:
        """注册Bean定义

        将Bean定义注册到注册表中，如果名称已存在则覆盖原有定义。

        Args:
            name: Bean名称，必须唯一
            bean_definition: Bean定义对象，包含Bean的创建信息

        Raises:
            BeanDefinitionError: 如果Bean定义无效
            IllegalArgumentError: 如果参数无效
        """
        pass

    @abstractmethod
    def get_definition(self, name: str) -> "BeanDefinition":
        """获取Bean定义

        根据Bean名称获取对应的Bean定义。

        Args:
            name: Bean名称

        Returns:
            BeanDefinition: Bean定义对象

        Raises:
            NoSuchBeanDefinitionError: 如果Bean定义不存在
        """
        pass

    @abstractmethod
    def has_definition(self, name: str) -> bool:
        """检查Bean定义是否存在

        检查指定名称的Bean定义是否已注册。

        Args:
            name: Bean名称

        Returns:
            bool: 如果Bean定义存在返回True，否则返回False
        """
        pass

    @abstractmethod
    def names(self) -> list[str]:
        """获取所有Bean定义名称

        返回注册表中所有Bean定义的名称列表。

        Returns:
            list[str]: Bean定义名称列表，如果没有定义则返回空列表
        """
        pass

    @abstractmethod
    def remove(self, name: str) -> None:
        """移除Bean定义

        从注册表中移除指定的Bean定义。

        Args:
            name: Bean名称

        Raises:
            NoSuchBeanDefinitionError: 如果Bean定义不存在
        """
        pass

    def count(self) -> int:
        """获取Bean定义数量

        Returns:
            int: Bean定义的总数量
        """
        return len(self.names())


class BeanPostProcessor(ABC):
    """Bean后置处理器接口

    在Bean初始化前后执行自定义处理逻辑。
    可用于实现AOP、属性注入、代理创建等功能。
    """

    @abstractmethod
    def post_process_before_initialization(self, bean: Any, _bean_name: str) -> Any:
        """Bean初始化前处理

        在Bean的初始化方法如@PostConstruct调用之前执行。
        可以修改Bean实例或返回代理对象。

        Args:
            bean: Bean实例
            bean_name: Bean名称

        Returns:
            Any: 处理后的Bean实例，可以是原实例或代理对象

        Raises:
            BeanCreationError: 如果处理过程中发生错误
        """
        return bean

    @abstractmethod
    def post_process_after_initialization(self, bean: Any, _bean_name: str) -> Any:
        """Bean初始化后处理

        在Bean的初始化方法如@PostConstruct调用之后执行。
        这是创建代理对象的最佳时机。

        Args:
            bean: Bean实例
            bean_name: Bean名称

        Returns:
            Any: 处理后的Bean实例，可以是原实例或代理对象

        Raises:
            BeanCreationError: 如果处理过程中发生错误
        """
        return bean


class InitializingBean(ABC):
    """初始化Bean接口

    实现此接口的Bean会在属性设置完成后自动调用after_properties_set方法。
    这是Bean自定义初始化逻辑的标准方式。
    """

    @abstractmethod
    def after_properties_set(self) -> None:
        """属性设置完成后的初始化方法

        在所有属性注入完成后调用用于执行Bean的初始化逻辑。
        此方法在@PostConstruct注解的方法之前调用。

        Raises:
            BeanInitializationError: 如果初始化过程中发生错误
        """
        pass


class DisposableBean(ABC):
    """可销毁Bean接口

    实现此接口的Bean会在容器关闭时自动调用destroy方法。
    用于释放资源、关闭连接等清理工作。
    """

    @abstractmethod
    def destroy(self) -> None:
        """销毁方法

        在Bean销毁时调用，用于执行清理工作。
        此方法在@PreDestroy注解的方法之后调用。

        Raises:
            Exception: 销毁过程中可能抛出的异常
        """
        pass


class BeanNameAware(ABC):
    """Bean名称感知接口

    实现此接口的Bean会在初始化时自动注入Bean名称。
    Bean可以通过此接口获知自己在容器中的名称。
    """

    @abstractmethod
    def set_bean_name(self, name: str) -> None:
        """设置Bean名称

        在Bean初始化过程中由容器调用，传入Bean的名称。

        Args:
            name: Bean在容器中的名称
        """
        pass


class BeanFactoryAware(ABC):
    """Bean工厂感知接口

    实现此接口的Bean会在初始化时自动注入Bean工厂。
    Bean可以通过此接口获取其他Bean或执行高级操作。
    """

    @abstractmethod
    def set_bean_factory(self, bean_factory: BeanFactory) -> None:
        """设置Bean工厂

        在Bean初始化过程中由容器调用传入Bean工厂实例。

        Args:
            bean_factory: Bean工厂实例
        """
        pass


class ApplicationContextAware(ABC):
    """应用上下文感知接口

    实现此接口的Bean会在初始化时自动注入应用上下文。
    Bean可以通过此接口访问应用上下文的所有功能。
    """

    @abstractmethod
    def set_application_context(self, application_context: "ApplicationContext") -> None:
        """设置应用上下文

        在Bean初始化过程中由容器调用，传入应用上下文实例。

        Args:
            application_context: 应用上下文实例
        """
        pass


class Lifecycle(ABC):
    """生命周期接口

    定义Bean的启动和停止方法。
    实现此接口的Bean可以参与容器的生命周期管理。
    """

    @abstractmethod
    def start(self) -> None:
        """启动方法

        在容器启动时调用，用于启动Bean的相关服务。

        Raises:
            Exception: 启动过程中可能抛出的异常
        """
        pass

    @abstractmethod
    def stop(self) -> None:
        """停止方法

        在容器停止时调用，用于停止Bean的相关服务。

        Raises:
            Exception: 停止过程中可能抛出的异常
        """
        pass

    @abstractmethod
    def is_running(self) -> bool:
        """检查是否正在运行

        返回Bean的运行状态。

        Returns:
            bool: 如果正在运行返回True，否则返回False
        """
        pass


class SmartLifecycle(Lifecycle):
    """智能生命周期接口

    扩展Lifecycle接口，提供更精细的生命周期控制。
    支持自动启动和阶段性启动。
    """

    def is_auto_startup(self) -> bool:
        """是否自动启动

        Returns:
            bool: 如果需要自动启动返回True，否则返回False
        """
        return True

    def get_phase(self) -> int:
        """获取启动阶段

        返回Bean的启动阶段，数值越小越早启动。

        Returns:
            int: 启动阶段，默认为0
        """
        return 0

    def stop(self, callback: Optional[callable] = None) -> None:
        """异步停止方法

        支持异步停止，完成后调用回调函数。

        Args:
            callback: 停止完成后的回调函数（可选）
        """
        super().stop()
        if callback:
            callback()
