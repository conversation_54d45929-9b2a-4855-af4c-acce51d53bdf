#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Bean监控诊断系统 - 提供Bean性能监控、依赖分析和健康检查功能
"""

import contextlib
import threading
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Optional

from .base import BeanFactory
from .definition import BeanDefinition


class MonitoringLevel(Enum):
    """监控级别"""

    BASIC = "basic"  # 基础监控
    DETAILED = "detailed"  # 详细监控
    FULL = "full"  # 完整监控


class HealthStatus(Enum):
    """健康状态"""

    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"


class DiagnosticLevel(Enum):
    """诊断级别"""

    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class BeanMetrics:
    """Bean性能指标"""

    bean_name: str
    creation_count: int = 0
    creation_time_ms: float = 0.0
    last_access_time: float = field(default_factory=time.time)
    access_count: int = 0
    error_count: int = 0
    average_creation_time_ms: float = 0.0
    peak_creation_time_ms: float = 0.0


@dataclass
class HealthCheckResult:
    """健康检查结果"""

    bean_name: str
    status: HealthStatus
    message: str
    details: dict[str, Any] = field(default_factory=dict)
    timestamp: float = field(default_factory=time.time)


@dataclass
class DiagnosticInfo:
    """诊断信息"""

    level: DiagnosticLevel
    category: str
    message: str
    details: dict[str, Any] = field(default_factory=dict)
    timestamp: float = field(default_factory=time.time)
    bean_name: Optional[str] = None


@dataclass
class PerformanceSnapshot:
    """性能快照"""

    timestamp: float
    total_beans: int
    active_beans: int
    total_creation_time_ms: float
    average_creation_time_ms: float
    error_rate: float
    memory_usage_mb: float = 0.0


class BeanMonitor:
    """Bean监控器

    提供Bean性能监控、健康检查和诊断功能。
    """

    def __init__(self, monitoring_level: MonitoringLevel = MonitoringLevel.BASIC):
        """初始化Bean监控器

        Args:
            monitoring_level: 监控级别
        """
        self.monitoring_level = monitoring_level
        self._lock = threading.RLock()

        # 监控数据
        self._bean_metrics: dict[str, BeanMetrics] = {}
        self._health_results: dict[str, HealthCheckResult] = {}
        self._diagnostic_info: deque = deque(maxlen=1000)
        self._performance_snapshots: deque = deque(maxlen=100)

        # 监控配置
        self._monitoring_enabled = True
        self._health_check_interval = 60.0  # 1分钟
        self._performance_snapshot_interval = 300.0  # 5分钟

        # 监控线程
        self._monitoring_thread: Optional[threading.Thread] = None
        self._monitoring_active = False

    def start_monitoring(self) -> None:
        """启动监控"""
        with self._lock:
            if not self._monitoring_active:
                self._monitoring_active = True
                self._monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
                self._monitoring_thread.start()

    def stop_monitoring(self) -> None:
        """停止监控"""
        with self._lock:
            self._monitoring_active = False
            if self._monitoring_thread:
                self._monitoring_thread.join(timeout=1.0)

    def record_creation(self, bean_name: str, creation_time_ms: float, success: bool = True) -> None:
        """记录Bean创建事件

        Args:
            bean_name: Bean名称
            creation_time_ms: 创建时间(毫秒)
            success: 是否创建成功
        """
        if not self._monitoring_enabled:
            return

        with self._lock:
            if bean_name not in self._bean_metrics:
                self._bean_metrics[bean_name] = BeanMetrics(bean_name=bean_name)

            metrics = self._bean_metrics[bean_name]
            metrics.creation_count += 1
            metrics.creation_time_ms += creation_time_ms
            metrics.last_access_time = time.time()

            if success:
                # 更新平均创建时间
                metrics.average_creation_time_ms = metrics.creation_time_ms / metrics.creation_count

                # 更新峰值创建时间
                if creation_time_ms > metrics.peak_creation_time_ms:
                    metrics.peak_creation_time_ms = creation_time_ms
            else:
                metrics.error_count += 1

            # 记录诊断信息
            if self.monitoring_level in [MonitoringLevel.DETAILED, MonitoringLevel.FULL]:
                level = DiagnosticLevel.INFO if success else DiagnosticLevel.ERROR
                message = f"Bean创建{'成功' if success else '失败'}: {creation_time_ms:.2f}ms"
                self._add_diagnostic_info(level, "bean_creation", message, bean_name=bean_name)

    def record_access(self, bean_name: str) -> None:
        """记录Bean访问事件

        Args:
            bean_name: Bean名称
        """
        if not self._monitoring_enabled:
            return

        with self._lock:
            if bean_name not in self._bean_metrics:
                self._bean_metrics[bean_name] = BeanMetrics(bean_name=bean_name)

            metrics = self._bean_metrics[bean_name]
            metrics.access_count += 1
            metrics.last_access_time = time.time()

    def perform_health_check(self, bean_name: str, bean: Any) -> HealthCheckResult:
        """执行Bean健康检查

        Args:
            bean_name: Bean名称
            bean: Bean实例

        Returns:
            HealthCheckResult: 健康检查结果
        """
        try:
            # 基础健康检查
            if bean is None:
                result = HealthCheckResult(bean_name=bean_name, status=HealthStatus.CRITICAL, message="Bean实例为None")
            else:
                # 检查Bean是否响应
                status = HealthStatus.HEALTHY
                message = "Bean状态正常"
                details = {}

                # 获取Bean指标
                if bean_name in self._bean_metrics:
                    metrics = self._bean_metrics[bean_name]
                    details["creation_count"] = metrics.creation_count
                    details["access_count"] = metrics.access_count
                    details["error_count"] = metrics.error_count
                    details["average_creation_time_ms"] = metrics.average_creation_time_ms

                    # 检查错误率
                    if metrics.creation_count > 0:
                        error_rate = metrics.error_count / metrics.creation_count
                        if error_rate > 0.1:  # 错误率超过10%
                            status = HealthStatus.WARNING
                            message = f"Bean错误率较高: {error_rate:.1%}"
                        elif error_rate > 0.2:  # 错误率超过20%
                            status = HealthStatus.CRITICAL
                            message = f"Bean错误率过高: {error_rate:.1%}"

                    # 检查创建时间
                    if metrics.average_creation_time_ms > 1000:  # 创建时间超过1秒
                        if status == HealthStatus.HEALTHY:
                            status = HealthStatus.WARNING
                            message = f"Bean创建时间较长: {metrics.average_creation_time_ms:.0f}ms"

                result = HealthCheckResult(bean_name=bean_name, status=status, message=message, details=details)

            # 保存健康检查结果
            with self._lock:
                self._health_results[bean_name] = result

            return result

        except Exception as e:
            result = HealthCheckResult(bean_name=bean_name, status=HealthStatus.UNKNOWN, message=f"健康检查失败: {e}")
            with self._lock:
                self._health_results[bean_name] = result
            return result

    def get_metrics(self, bean_name: Optional[str] = None) -> dict[str, BeanMetrics]:
        """获取Bean性能指标

        Args:
            bean_name: Bean名称，为None时返回所有Bean的指标

        Returns:
            dict[str, BeanMetrics]: Bean性能指标字典
        """
        with self._lock:
            if bean_name:
                return {bean_name: self._bean_metrics.get(bean_name)} if bean_name in self._bean_metrics else {}
            return self._bean_metrics.copy()

    def get_health_status(self, bean_name: Optional[str] = None) -> dict[str, HealthCheckResult]:
        """获取Bean健康状态

        Args:
            bean_name: Bean名称，为None时返回所有Bean的健康状态

        Returns:
            dict[str, HealthCheckResult]: Bean健康状态字典
        """
        with self._lock:
            if bean_name:
                return {bean_name: self._health_results.get(bean_name)} if bean_name in self._health_results else {}
            return self._health_results.copy()

    def get_diagnostic_info(self, level: Optional[DiagnosticLevel] = None, hours: int = 24) -> list[DiagnosticInfo]:
        """获取诊断信息

        Args:
            level: 诊断级别过滤
            hours: 时间范围(小时)

        Returns:
            list[DiagnosticInfo]: 诊断信息列表
        """
        with self._lock:
            cutoff_time = time.time() - (hours * 3600)
            diagnostics = [info for info in self._diagnostic_info if info.timestamp >= cutoff_time]

            if level:
                diagnostics = [info for info in diagnostics if info.level == level]

            return sorted(diagnostics, key=lambda x: x.timestamp, reverse=True)

    def get_performance_summary(self) -> dict[str, Any]:
        """获取性能摘要"""
        with self._lock:
            if not self._bean_metrics:
                return {}

            total_beans = len(self._bean_metrics)
            total_creations = sum(metrics.creation_count for metrics in self._bean_metrics.values())
            total_creation_time = sum(metrics.creation_time_ms for metrics in self._bean_metrics.values())
            total_errors = sum(metrics.error_count for metrics in self._bean_metrics.values())

            avg_creation_time = total_creation_time / total_creations if total_creations > 0 else 0
            error_rate = total_errors / total_creations if total_creations > 0 else 0

            # 健康状态统计
            health_counts = defaultdict(int)
            for result in self._health_results.values():
                health_counts[result.status.value] += 1

            return {
                "total_beans": total_beans,
                "total_creations": total_creations,
                "total_creation_time_ms": total_creation_time,
                "average_creation_time_ms": avg_creation_time,
                "total_errors": total_errors,
                "error_rate": error_rate,
                "health_status_counts": dict(health_counts),
                "monitoring_level": self.monitoring_level.value,
                "monitoring_active": self._monitoring_active,
            }

    def _add_diagnostic_info(self, level: DiagnosticLevel, category: str, message: str, bean_name: Optional[str] = None, **details) -> None:
        """添加诊断信息"""
        diagnostic = DiagnosticInfo(level=level, category=category, message=message, bean_name=bean_name, details=details)
        self._diagnostic_info.append(diagnostic)

    def _monitoring_loop(self) -> None:
        """监控循环"""
        last_health_check = 0
        last_performance_snapshot = 0

        while self._monitoring_active:
            current_time = time.time()

            with contextlib.suppress(Exception):
                # 定期健康检查
                if current_time - last_health_check >= self._health_check_interval:
                    self._health_check()
                    last_health_check = current_time

                # 定期性能快照
                if current_time - last_performance_snapshot >= self._performance_snapshot_interval:
                    self._snapshot()
                    last_performance_snapshot = current_time

            time.sleep(1.0)  # 1秒检查间隔

    def _health_check(self) -> None:
        """执行定期健康检查"""
        # 这里可以添加定期健康检查逻辑
        # 目前作为框架预留
        pass

    def _snapshot(self) -> None:
        """获取性能快照"""
        summary = self.get_performance_summary()
        if summary:
            snapshot = PerformanceSnapshot(
                timestamp=time.time(),
                total_beans=summary["total_beans"],
                active_beans=len([m for m in self._bean_metrics.values() if m.access_count > 0]),
                total_creation_time_ms=summary["total_creation_time_ms"],
                average_creation_time_ms=summary["average_creation_time_ms"],
                error_rate=summary["error_rate"],
            )
            self._performance_snapshots.append(snapshot)


class BeanDiagnostics:
    """Bean诊断系统

    提供Bean依赖分析、性能诊断和问题检测功能。
    """

    def __init__(self):
        self._lock = threading.RLock()
        self._diagnostic_rules: list[callable] = []
        self._analysis_cache: dict[str, Any] = {}

    def add_diagnostic_rule(self, rule: callable) -> None:
        """添加诊断规则

        Args:
            rule: 诊断规则函数，接收Bean信息并返回诊断结果
        """
        with self._lock:
            self._diagnostic_rules.append(rule)

    def diagnose_bean(self, bean_name: str, bean_definition: BeanDefinition, metrics: BeanMetrics) -> list[DiagnosticInfo]:
        """诊断单个Bean

        Args:
            bean_name: Bean名称
            bean_definition: Bean定义
            metrics: Bean性能指标

        Returns:
            list[DiagnosticInfo]: 诊断结果列表
        """
        diagnostics = []

        # 执行内置诊断规则
        diagnostics.extend(self._diagnose_performance(bean_name, metrics))
        diagnostics.extend(self._diagnose_configuration(bean_name, bean_definition))

        # 执行自定义诊断规则
        for rule in self._diagnostic_rules:
            try:
                result = rule(bean_name, bean_definition, metrics)
                if result:
                    if isinstance(result, list):
                        diagnostics.extend(result)
                    else:
                        diagnostics.append(result)
            except Exception as e:
                diagnostics.append(
                    DiagnosticInfo(
                        level=DiagnosticLevel.ERROR,
                        category="diagnostic_rule",
                        message=f"诊断规则执行失败: {e}",
                        bean_name=bean_name,
                    )
                )

        return diagnostics

    def _diagnose_performance(self, bean_name: str, metrics: BeanMetrics) -> list[DiagnosticInfo]:
        """性能诊断"""
        diagnostics = []

        # 检查创建时间
        if metrics.average_creation_time_ms > 500:
            level = DiagnosticLevel.WARNING if metrics.average_creation_time_ms < 1000 else DiagnosticLevel.ERROR
            diagnostics.append(
                DiagnosticInfo(
                    level=level,
                    category="performance",
                    message=f"Bean创建时间过长: {metrics.average_creation_time_ms:.0f}ms",
                    bean_name=bean_name,
                    details={"average_creation_time_ms": metrics.average_creation_time_ms},
                )
            )

        # 检查错误率
        if metrics.creation_count > 0:
            error_rate = metrics.error_count / metrics.creation_count
            if error_rate > 0.05:  # 错误率超过5%
                level = DiagnosticLevel.WARNING if error_rate < 0.1 else DiagnosticLevel.ERROR
                diagnostics.append(
                    DiagnosticInfo(
                        level=level,
                        category="reliability",
                        message=f"Bean创建错误率过高: {error_rate:.1%}",
                        bean_name=bean_name,
                        details={"error_rate": error_rate, "error_count": metrics.error_count},
                    )
                )

        # 检查使用频率
        if metrics.creation_count > 0 and metrics.access_count == 0:
            diagnostics.append(
                DiagnosticInfo(
                    level=DiagnosticLevel.INFO,
                    category="usage",
                    message="Bean已创建但从未被访问",
                    bean_name=bean_name,
                    details={"creation_count": metrics.creation_count, "access_count": metrics.access_count},
                )
            )

        return diagnostics

    def _diagnose_configuration(self, bean_name: str, bean_definition: BeanDefinition) -> list[DiagnosticInfo]:
        """配置诊断"""
        diagnostics = []

        # 检查Bean名称
        if not bean_name or len(bean_name) < 2:
            diagnostics.append(
                DiagnosticInfo(
                    level=DiagnosticLevel.WARNING,
                    category="configuration",
                    message="Bean名称过短或为空",
                    bean_name=bean_name,
                )
            )

        # 检查Bean类
        if not bean_definition.bean_class:
            diagnostics.append(
                DiagnosticInfo(
                    level=DiagnosticLevel.ERROR,
                    category="configuration",
                    message="Bean类未定义",
                    bean_name=bean_name,
                )
            )

        return diagnostics

    def analyze_deps(self, bean_definitions: dict[str, BeanDefinition]) -> list[DiagnosticInfo]:
        """分析依赖问题

        Args:
            bean_definitions: Bean定义字典

        Returns:
            list[DiagnosticInfo]: 依赖问题诊断结果
        """
        diagnostics = []

        # 使用统一的依赖图进行循环依赖检测
        from .graph import DependencyGraph

        dependency_graph = DependencyGraph()

        # 构建依赖图
        for bean_name, definition in bean_definitions.items():
            dependencies = getattr(definition, "dependencies", [])
            for dep in dependencies:
                dependency_graph.add_dependency(bean_name, dep)

        # 检查循环依赖
        for bean_name in bean_definitions:
            if dependency_graph.has_circular_dependency(bean_name):
                cycle = dependency_graph.get_circular_dependency_path(bean_name)
                diagnostics.append(
                    DiagnosticInfo(
                        level=DiagnosticLevel.ERROR,
                        category="dependency",
                        message=f"检测到循环依赖: {' -> '.join(cycle)}",
                        details={"cycle": cycle},
                    )
                )

        # 检查缺失依赖
        missing_deps = self._detect_missing_dependencies(bean_definitions)
        for bean_name, missing in missing_deps.items():
            diagnostics.append(
                DiagnosticInfo(
                    level=DiagnosticLevel.ERROR,
                    category="dependency",
                    message=f"Bean '{bean_name}' 存在缺失依赖: {', '.join(missing)}",
                    bean_name=bean_name,
                    details={"missing_dependencies": missing},
                )
            )

        return diagnostics

    # 已删除重复的循环依赖检测逻辑 - 使用 DependencyGraph 统一实现

    def _detect_missing_dependencies(self, bean_definitions: dict[str, BeanDefinition]) -> dict[str, list[str]]:
        """检测缺失依赖"""
        missing_deps = {}

        for bean_name, definition in bean_definitions.items():
            dependencies = getattr(definition, "dependencies", [])
            missing = []

            for dep in dependencies:
                if dep not in bean_definitions:
                    missing.append(dep)

            if missing:
                missing_deps[bean_name] = missing

        return missing_deps


class MonitoredBeanFactory(BeanFactory):
    """带监控的Bean工厂

    集成监控和诊断功能的Bean工厂。
    """

    def __init__(self, monitoring_level: MonitoringLevel = MonitoringLevel.BASIC):
        """初始化监控Bean工厂

        Args:
            monitoring_level: 监控级别
        """
        super().__init__()
        self._monitor = BeanMonitor(monitoring_level)
        self._diagnostics = BeanDiagnostics()

        # 启动监控
        self._monitor.start_monitoring()

    def create_bean(self, bean_definition: BeanDefinition, *args, **kwargs) -> Any:
        """创建Bean实例（带监控）"""
        start_time = time.time()
        success = False
        bean = None

        try:
            # 调用父类方法创建Bean
            bean = super().create_bean(bean_definition, *args, **kwargs)
            success = True
            return bean

        except Exception as e:
            # 记录创建失败
            self._monitor._add_diagnostic_info(DiagnosticLevel.ERROR, "bean_creation", f"Bean创建失败: {e}", bean_name=bean_definition.bean_name)
            raise

        finally:
            # 记录创建事件
            creation_time_ms = (time.time() - start_time) * 1000
            self._monitor.record_creation(bean_definition.bean_name, creation_time_ms, success)

            # 执行健康检查
            if success and bean is not None:
                self._monitor.perform_health_check(bean_definition.bean_name, bean)

    def get_bean(self, bean_name: str, *args, **kwargs) -> Any:
        """获取Bean实例（带监控）"""
        # 记录访问事件
        self._monitor.record_access(bean_name)

        # 调用父类方法
        return super().get_bean(bean_name, *args, **kwargs)

    def get_summary(self) -> dict[str, Any]:
        """获取监控摘要"""
        return {
            "performance_summary": self._monitor.get_performance_summary(),
            "health_status": self._monitor.get_health_status(),
            "recent_diagnostics": self._monitor.get_diagnostic_info(hours=1),
        }

    def diagnose_all(self) -> dict[str, list[DiagnosticInfo]]:
        """诊断所有Bean"""
        results = {}
        metrics = self._monitor.get_metrics()

        # 获取Bean定义（这里需要子类实现具体逻辑）
        bean_definitions = self._get_bean_definitions()

        for bean_name, definition in bean_definitions.items():
            bean_metrics = metrics.get(bean_name)
            if bean_metrics:
                results[bean_name] = self._diagnostics.diagnose_bean(bean_name, definition, bean_metrics)

        return results

    def _get_bean_definitions(self) -> dict[str, BeanDefinition]:
        """获取Bean定义（子类需要实现）"""
        # 这里返回空字典，子类应该重写此方法
        return {}


# 便捷函数已删除 - 请直接使用类构造器
# 使用方式：MonitoredBeanFactory(monitoring_level)
