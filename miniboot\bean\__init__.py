"""
Bean 管理模块

提供 Mini-Boot 框架的 Bean 定义、创建、注册和依赖解析功能。


"""

# 导入核心异常类
from ..errors import BeanCircularDependencyError as CircularDependencyError
from ..errors import BeanCreationError
from ..errors import BeanNotFoundError as NoSuchBeanDefinitionError
from ..errors import MultipleBeanFoundError as NoUniqueBeanDefinitionError

# 导入核心接口和基类
from .base import (
    ApplicationContextAware,
    BeanDefinitionRegistry,
    BeanFactory,
    BeanFactoryAware,
    BeanNameAware,
    BeanPostProcessor,
    ConfigurableBeanFactory,
    DisposableBean,
    HierarchicalBeanFactory,
    InitializingBean,
    Lifecycle,
    ListableBeanFactory,
    SmartLifecycle,
)

# 导入核心实现类
from .definition import BeanDefinition, BeanScope, ConstructorArgument, PropertyValue

# 导入分层工厂
from .factory import DefaultListableBeanFactory

# 导入依赖图
from .graph import DependencyGraph

# 导入高性能依赖注入器
from .injector import (
    AsyncDependencyInjector,
    BeanDefinitionAdapter,
    CompileTimeAnalyzer,
    HighPerformanceDependencyInjector,
    HighPerformanceInjectorManager,
    InjectionPlan,
    ZeroReflectionInjector,
)

# 导入生命周期管理
from .lifecycle import LifecycleManager

# 导入内存优化功能
from .memory import (
    BeanMemoryInfo,
    MemoryAlert,
    MemoryAnalyzer,
    MemoryConfig,
    MemoryGeneration,
    MemoryMonitor,
    MemoryOptimizedBeanFactory,
    MemoryOptimizer,
    MemoryStats,
    MemoryTrend,
)

# 导入监控诊断功能
from .monitor import (
    BeanDiagnostics,
    BeanMetrics,
    BeanMonitor,
    DiagnosticInfo,
    DiagnosticLevel,
    HealthCheckResult,
    HealthStatus,
    MonitoredBeanFactory,
    MonitoringLevel,
    PerformanceSnapshot,
)

# 导入Bean创建优化器
from .optimizer import (
    BatchCreationManager,
    BeanCreationOptimizer,
    CachedCreationStrategy,
    CreationConfig,
    CreationMetrics,
    CreationPerformanceMonitor,
    CreationStrategy,
    DirectCreationStrategy,
    OptimizedBeanFactory,
    PooledCreationStrategy,
    PreCreationManager,
)
from .registry import DefaultBeanDefinitionRegistry

# 导入核心作用域管理
from .scopes import BeanScopeContext, BeanScopeManager, BeanScopeRegistry, PrototypeScopeManager, SingletonScopeManager


__all__ = [
    # === 核心Bean定义 ===
    "BeanDefinition",  # Bean元数据定义
    "BeanScope",  # Bean作用域枚举
    "PropertyValue",  # Bean属性值
    "ConstructorArgument",  # 构造函数参数
    # === 核心接口 ===
    "BeanFactory",  # Bean工厂基础接口
    "HierarchicalBeanFactory",  # 分层Bean工厂接口
    "ConfigurableBeanFactory",  # 可配置Bean工厂接口
    "ListableBeanFactory",  # 可列举Bean工厂接口
    "BeanDefinitionRegistry",  # Bean定义注册表接口
    # === 生命周期接口 ===
    "BeanPostProcessor",  # Bean后置处理器
    "InitializingBean",  # 初始化Bean接口
    "DisposableBean",  # 可销毁Bean接口
    "BeanNameAware",  # Bean名称感知接口
    "BeanFactoryAware",  # Bean工厂感知接口
    "ApplicationContextAware",  # 应用上下文感知接口
    "Lifecycle",  # 生命周期接口
    "SmartLifecycle",  # 智能生命周期接口
    # === 核心实现类 ===
    "DefaultListableBeanFactory",  # 默认分层Bean工厂实现（推荐）
    "DefaultBeanDefinitionRegistry",  # 默认Bean定义注册表实现
    "LifecycleManager",  # 生命周期管理器
    # === 核心作用域管理 ===
    "BeanScopeManager",  # 核心作用域管理器接口
    "BeanScopeRegistry",  # 核心作用域注册表
    "BeanScopeContext",  # 核心作用域上下文
    "SingletonScopeManager",  # 单例作用域管理器
    "PrototypeScopeManager",  # 原型作用域管理器
    # === 工具类 ===
    "DependencyGraph",  # 依赖关系图
    # === 核心异常类 ===
    "BeanCreationError",  # Bean创建异常(最常见)
    "NoSuchBeanDefinitionError",  # Bean不存在异常(最常见)
    "NoUniqueBeanDefinitionError",  # Bean不唯一异常
    "CircularDependencyError",  # 循环依赖异常(重要)
    # === 高性能依赖注入器 ===
    "HighPerformanceDependencyInjector",  # 高性能依赖注入器
    "CompileTimeAnalyzer",  # 编译时分析器
    "ZeroReflectionInjector",  # 零反射注入器
    "AsyncDependencyInjector",  # 异步依赖注入器
    "InjectionPlan",  # 注入计划
    "BeanDefinitionAdapter",  # Bean定义适配器
    "HighPerformanceInjectorManager",  # 高性能注入器管理器
    # === Bean创建优化器 ===
    "BeanCreationOptimizer",  # Bean创建优化器
    "BatchCreationManager",  # 批量创建管理器
    "PreCreationManager",  # 预创建管理器
    "CreationPerformanceMonitor",  # 创建性能监控器
    "CreationConfig",  # 创建配置
    "CreationStrategy",  # 创建策略枚举
    "CreationMetrics",  # 创建指标
    "DirectCreationStrategy",  # 直接创建策略
    "CachedCreationStrategy",  # 缓存创建策略
    "PooledCreationStrategy",  # 池化创建策略
    "OptimizedBeanFactory",  # 优化Bean工厂
    # === 内存优化功能 ===
    "MemoryOptimizer",  # 内存优化器
    "MemoryMonitor",  # 内存监控器
    "MemoryAnalyzer",  # 内存分析器
    "MemoryConfig",  # 内存配置
    "MemoryOptimizedBeanFactory",  # 内存优化Bean工厂
    "MemoryStats",  # 内存统计
    "BeanMemoryInfo",  # Bean内存信息
    "MemoryAlert",  # 内存警报
    "MemoryTrend",  # 内存趋势
    "MemoryGeneration",  # 内存分代
    # === 监控诊断功能 ===
    "BeanMonitor",  # Bean监控器
    "BeanDiagnostics",  # Bean诊断系统
    "MonitoredBeanFactory",  # 监控Bean工厂
    "BeanMetrics",  # Bean指标
    "HealthCheckResult",  # 健康检查结果
    "DiagnosticInfo",  # 诊断信息
    "PerformanceSnapshot",  # 性能快照
    "HealthStatus",  # 健康状态
    "MonitoringLevel",  # 监控级别
    "DiagnosticLevel",  # 诊断级别
]


__version__ = "2.0.0"
