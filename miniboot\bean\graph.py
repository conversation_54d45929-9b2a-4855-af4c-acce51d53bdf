#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Bean依赖关系图管理
"""

import threading
from collections import defaultdict, deque
from typing import Any, Optional

# 导入标准化的异常类
from ..errors import BeanCircularDependencyError as CircularDependencyError


class DependencyGraph:
    """Bean依赖关系图

    管理Bean之间的依赖关系，支持循环依赖检测和拓扑排序。
    提供高性能的依赖分析和优化功能。
    """

    def __init__(self):
        """初始化依赖关系图"""
        self._dependencies: dict[str, set[str]] = defaultdict(set)  # Bean名称 -> 依赖的Bean名称集合
        self._dependents: dict[str, set[str]] = defaultdict(set)  # Bean名称 -> 依赖它的Bean名称集合
        self._creating_beans: set[str] = set()  # 正在创建中的Bean名称
        self._created_beans: set[str] = set()  # 已创建完成的Bean名称
        self._lock = threading.RLock()  # 线程安全锁

        # 性能优化缓存
        self._circular_check_cache: dict[str, bool] = {}  # Bean名称 -> 是否有循环依赖
        self._topological_order_cache: Optional[list[str]] = None  # 拓扑排序缓存
        self._cache_version = 0  # 缓存版本号

        # 性能统计
        self._stats = {"cache_hits": 0, "cache_misses": 0, "circular_checks": 0, "dependency_additions": 0}

    def add_dependency(self, bean_name: str, dependency_name: str) -> None:
        """添加依赖关系

        Args:
            bean_name: Bean名称
            dependency_name: 依赖的Bean名称

        Raises:
            CircularDependencyError: 如果添加依赖会造成循环依赖
        """
        if bean_name == dependency_name:
            raise CircularDependencyError(f"Bean cannot depend on itself: {bean_name}")

        with self._lock:
            # 检查是否已存在此依赖关系
            if dependency_name in self._dependencies[bean_name]:
                return  # 依赖关系已存在，无需重复添加

            # 检查添加此依赖是否会造成循环依赖
            if self._would_create_cycle(bean_name, dependency_name):
                cycle_path = self._find_cycle_path(dependency_name, bean_name)
                cycle_chain = cycle_path + [bean_name]
                raise CircularDependencyError(f"Circular dependency detected: {' -> '.join(cycle_chain)}", dependency_chain=cycle_chain)

            # 添加依赖关系
            self._dependencies[bean_name].add(dependency_name)
            self._dependents[dependency_name].add(bean_name)

            # 清除缓存
            self._invalidate_cache()
            self._stats["dependency_additions"] += 1

    def remove_dependency(self, bean_name: str, dependency_name: str) -> None:
        """移除依赖关系

        Args:
            bean_name: Bean名称
            dependency_name: 依赖的Bean名称
        """
        with self._lock:
            self._dependencies[bean_name].discard(dependency_name)
            self._dependents[dependency_name].discard(bean_name)
            self._invalidate_cache()

    def get_dependencies(self, bean_name: str) -> set[str]:
        """获取Bean的所有依赖

        Args:
            bean_name: Bean名称

        Returns:
            Set[str]: 依赖的Bean名称集合
        """
        with self._lock:
            return self._dependencies[bean_name].copy()

    def get_dependents(self, bean_name: str) -> set[str]:
        """获取依赖指定Bean的所有Bean

        Args:
            bean_name: Bean名称

        Returns:
            Set[str]: 依赖此Bean的Bean名称集合
        """
        with self._lock:
            return self._dependents[bean_name].copy()

    def has_circular_dependency(self, bean_name: str) -> bool:
        """检查Bean是否存在循环依赖

        Args:
            bean_name: Bean名称

        Returns:
            bool: 如果存在循环依赖返回True，否则返回False
        """
        with self._lock:
            # 检查缓存
            if bean_name in self._circular_check_cache:
                self._stats["cache_hits"] += 1
                return self._circular_check_cache[bean_name]

            self._stats["cache_misses"] += 1
            self._stats["circular_checks"] += 1

            # 使用DFS检测循环依赖
            visited = set()
            rec_stack = set()

            def dfs(node: str) -> bool:
                if node in rec_stack:
                    return True
                if node in visited:
                    return False

                visited.add(node)
                rec_stack.add(node)

                for dependency in self._dependencies[node]:
                    if dfs(dependency):
                        return True

                rec_stack.remove(node)
                return False

            has_cycle = dfs(bean_name)
            self._circular_check_cache[bean_name] = has_cycle
            return has_cycle

    def get_circular_dependency_path(self, bean_name: str) -> list[str]:
        """获取循环依赖路径

        Args:
            bean_name: Bean名称

        Returns:
            List[str]: 循环依赖路径，如果没有循环依赖返回空列表
        """
        with self._lock:
            if not self.has_circular_dependency(bean_name):
                return []
            return self._find_any_cycle_from(bean_name)

    def get_creation_order(self) -> list[str]:
        """获取Bean的创建顺序（拓扑排序）

        Returns:
            List[str]: Bean创建顺序列表

        Raises:
            CircularDependencyError: 如果存在循环依赖
        """
        with self._lock:
            # 检查缓存
            if self._topological_order_cache is not None:
                return self._topological_order_cache.copy()

            # 执行拓扑排序
            all_beans = set(self._dependencies.keys()) | set(self._dependents.keys())
            in_degree = dict.fromkeys(all_beans, 0)

            # 计算入度
            for bean in all_beans:
                for dependency in self._dependencies[bean]:
                    in_degree[dependency] += 1

            # 使用队列进行拓扑排序
            queue = deque([bean for bean in all_beans if in_degree[bean] == 0])
            result = []

            while queue:
                bean = queue.popleft()
                result.append(bean)

                # 更新依赖此Bean的其他Bean的入度
                for dependent in self._dependents[bean]:
                    in_degree[dependent] -= 1
                    if in_degree[dependent] == 0:
                        queue.append(dependent)

            # 检查是否存在循环依赖
            if len(result) != len(all_beans):
                remaining_beans = all_beans - set(result)
                for bean in remaining_beans:
                    if self.has_circular_dependency(bean):
                        cycle_path = self._find_any_cycle_from(bean)
                        raise CircularDependencyError(f"Circular dependency detected: {' -> '.join(cycle_path)}", dependency_chain=cycle_path)

            # 缓存结果
            self._topological_order_cache = result.copy()
            return result

    def _would_create_cycle(self, bean_name: str, dependency_name: str) -> bool:
        """检查添加依赖是否会创建循环

        Args:
            bean_name: Bean名称
            dependency_name: 依赖的Bean名称

        Returns:
            bool: 如果会创建循环返回True，否则返回False
        """
        # 使用DFS检查从dependency_name是否能到达bean_name
        visited = set()

        def dfs(node: str) -> bool:
            if node == bean_name:
                return True
            if node in visited:
                return False

            visited.add(node)
            for dep in self._dependencies[node]:
                if dfs(dep):
                    return True
            return False

        return dfs(dependency_name)

    def _find_cycle_path(self, start: str, target: str) -> list[str]:
        """查找从start到target的路径

        Args:
            start: 起始Bean名称
            target: 目标Bean名称

        Returns:
            List[str]: 路径列表
        """
        visited = set()
        path = []

        def dfs(node: str) -> bool:
            if node in visited:
                return False
            if node == target:
                path.append(node)
                return True

            visited.add(node)
            path.append(node)

            for dep in self._dependencies[node]:
                if dfs(dep):
                    return True

            path.pop()
            return False

        dfs(start)
        return path

    def _find_any_cycle_from(self, bean_name: str) -> list[str]:
        """从指定Bean查找任意循环路径

        Args:
            bean_name: Bean名称

        Returns:
            List[str]: 循环路径
        """
        visited = set()
        rec_stack = set()
        path = []

        def dfs(node: str) -> bool:
            if node in rec_stack:
                # 找到循环，构建循环路径
                cycle_start = path.index(node)
                return True
            if node in visited:
                return False

            visited.add(node)
            rec_stack.add(node)
            path.append(node)

            for dependency in self._dependencies[node]:
                if dfs(dependency):
                    return True

            rec_stack.remove(node)
            path.pop()
            return False

        if dfs(bean_name):
            return path
        return [bean_name]  # 如果没找到循环，返回单个Bean

    def _invalidate_cache(self) -> None:
        """清除缓存"""
        self._circular_check_cache.clear()
        self._topological_order_cache = None
        self._cache_version += 1

    def clear(self) -> None:
        """清空依赖图"""
        with self._lock:
            self._dependencies.clear()
            self._dependents.clear()
            self._creating_beans.clear()
            self._created_beans.clear()
            self._invalidate_cache()

    def get_stats(self) -> dict[str, Any]:
        """获取统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        with self._lock:
            return {
                "bean_count": len(set(self._dependencies.keys()) | set(self._dependents.keys())),
                "dependency_count": sum(len(deps) for deps in self._dependencies.values()),
                "cache_version": self._cache_version,
                "stats": self._stats.copy(),
            }
