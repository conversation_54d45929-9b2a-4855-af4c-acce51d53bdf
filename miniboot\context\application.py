#!/usr/bin/env python
"""
* @author: cz
* @description: 应用上下文接口和实现

实现Mini-Boot框架的核心应用上下文,提供Bean管理、环境配置、事件发布等功能.
"""

import asyncio
import os
import threading
from abc import ABC, abstractmethod
from contextlib import suppress
from enum import Enum
from pathlib import Path
from typing import Any, Optional, TypeVar

from loguru import logger

from ..errors import BeanCreationError, BeanNotFoundError, ContextShutdownError, ContextStartupError, EventPublishError, PropertyNotFoundError


T = TypeVar("T")


from ..annotations import ComponentScanner

# 使用简化的Bean API
# 核心功能从新模块导入
from ..bean import BeanDefinition, BeanDefinitionRegistry, BeanFactory, DefaultBeanDefinitionRegistry, DependencyGraph
from ..env import Environment, StandardEnvironment
from ..events import ApplicationEventPublisher, EventPublisher
from ..processor.registry import BeanPostProcessorRegistry
from .runtime_detector import RuntimeDetector


class ApplicationContextState(Enum):
    """应用上下文状态枚举"""

    STOPPED = "stopped"  # 已停止状态
    STARTING = "starting"  # 启动中状态
    RUNNING = "running"  # 运行中状态
    STOPPING = "stopping"  # 停止中状态


class ApplicationContext(ABC):
    """应用上下文接口

    定义应用上下文的核心功能,包括Bean管理、环境配置、事件发布等.
    这是Mini-Boot框架的核心接口,所有应用上下文实现都应该实现此接口.
    """

    @abstractmethod
    async def start(self) -> None:
        """启动应用上下文

        启动应用上下文及其所有组件,包括:
        - 初始化核心组件
        - 扫描和注册Bean
        - 创建单例Bean
        - 启动生命周期组件
        - 发布启动事件

        Raises:
            ContextStartupError: 启动失败时抛出
        """
        pass

    @abstractmethod
    async def stop(self) -> None:
        """停止应用上下文

        停止应用上下文及其所有组件,包括:
        - 停止生命周期组件
        - 销毁Bean实例
        - 关闭资源连接
        - 发布停止事件

        Raises:
            ContextShutdownError: 停止失败时抛出
        """
        pass

    @abstractmethod
    def is_running(self) -> bool:
        """检查应用上下文是否处于运行状态

        Returns:
            bool: 如果上下文正在运行返回True,否则返回False
        """
        pass

    @abstractmethod
    def register_type(self, cls: type[T], name: Optional[str] = None) -> None:
        """注册Bean类型到容器中

        Args:
            cls: Bean类型
            name: Bean名称,如果为None则使用类名的小写形式

        Raises:
            BeanDefinitionError: Bean定义错误时抛出
        """
        pass

    @abstractmethod
    def get_bean(self, name: str) -> Any:
        """根据名称获取Bean实例

        Args:
            name: Bean名称

        Returns:
            Any: Bean实例

        Raises:
            BeanNotFoundError: Bean不存在时抛出
            BeanCreationError: Bean创建失败时抛出
        """
        pass

    @abstractmethod
    def get_bean_by_type(self, cls: type[T]) -> Optional[T]:
        """根据类型获取Bean实例

        Args:
            cls: Bean类型

        Returns:
            Optional[T]: Bean实例,如果不存在返回None

        Raises:
            BeanCreationError: Bean创建失败时抛出
        """
        pass

    @abstractmethod
    def get_beans_by_type(self, cls: type[T]) -> dict[str, T]:
        """根据类型获取所有Bean实例

        Args:
            cls: Bean类型

        Returns:
            Dict[str, T]: Bean名称到实例的映射

        Raises:
            BeanCreationError: Bean创建失败时抛出
        """
        pass

    @abstractmethod
    def contains_bean(self, name: str) -> bool:
        """检查是否包含指定名称的Bean

        Args:
            name: Bean名称

        Returns:
            bool: 如果包含返回True,否则返回False
        """
        pass

    @abstractmethod
    def get_property(self, key: str, default: Any = None) -> Any:
        """获取配置属性

        Args:
            key: 属性键
            default: 默认值

        Returns:
            Any: 属性值,如果不存在返回默认值
        """
        pass

    @abstractmethod
    def publish_event(self, event: Any) -> None:
        """发布事件(同步)

        Args:
            event: 事件对象

        Raises:
            EventPublishError: 事件发布失败时抛出
        """
        pass

    @abstractmethod
    async def publish_event_async(self, event: Any) -> None:
        """发布事件(异步)

        Args:
            event: 事件对象

        Raises:
            EventPublishError: 事件发布失败时抛出
        """
        pass

    @abstractmethod
    def get_environment(self):
        """获取环境配置对象

        Returns:
            Environment: 环境配置对象
        """
        pass

    @abstractmethod
    def get_bean_factory(self):
        """获取Bean工厂

        Returns:
            BeanFactory: Bean工厂实例
        """
        pass

    @abstractmethod
    def refresh(self) -> None:
        """刷新应用上下文

        重新加载配置和重新创建Bean实例.

        Raises:
            ApplicationContextError: 刷新失败时抛出
        """
        pass

    @abstractmethod
    def close(self) -> None:
        """关闭应用上下文

        同步版本的停止方法,用于在非异步环境中关闭上下文.

        Raises:
            ContextShutdownError: 关闭失败时抛出
        """
        pass

    # 上下文管理器支持
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.stop()

    def __enter__(self):
        """同步上下文管理器入口"""
        # 在同步环境中启动异步上下文需要特殊处理
        loop = None
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        if loop.is_running():
            # 如果事件循环正在运行,创建任务
            asyncio.create_task(self.start())
        else:
            # 如果事件循环未运行,直接运行
            loop.run_until_complete(self.start())

        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """同步上下文管理器出口"""
        self.close()


class DefaultApplicationContext(ApplicationContext):
    """默认应用上下文实现

    提供ApplicationContext接口的默认实现,集成了所有核心功能.
    作为协调者,管理和集成各个模块,而不是重新实现它们.
    """

    def __init__(self, config_path: Optional[str] = None, packages_to_scan: Optional[list[str]] = None, auto_detect: bool = True):
        """初始化默认应用上下文

        Args:
            config_path: 配置文件路径
            packages_to_scan: 要扫描的包列表
            auto_detect: 是否自动检测异步环境
        """
        # 初始化基础状态
        self._init_basic_state()

        # 初始化核心组件
        self._init_components()

        # 初始化配置参数
        self._init_configuration(config_path, packages_to_scan)

        # 初始化智能功能
        self._init_smart_features(auto_detect)

        logger.info("DefaultApplicationContext initialized with integrated smart features")

    def _init_basic_state(self) -> None:
        """初始化基础状态"""
        self._state = ApplicationContextState.STOPPED  # 应用上下文状态
        self._lock = threading.RLock()  # 线程锁
        self._async_lock = None  # 异步锁(延迟创建)
        self._shutdown_hooks = []  # 停止钩子列表

    def _init_components(self) -> None:
        """初始化核心组件"""
        # 核心组件集成(环境将在启动时初始化)
        self._environment: Environment = None
        self._bean_registry: BeanDefinitionRegistry = DefaultBeanDefinitionRegistry()
        self._dependency_graph: DependencyGraph = DependencyGraph()
        # 使用智能Bean工厂(延迟初始化,在检测到环境后创建)
        self._bean_factory: BeanFactory = None
        self._event_publisher: EventPublisher = ApplicationEventPublisher()
        self._component_scanner: ComponentScanner = ComponentScanner()
        self._processor_registry: BeanPostProcessorRegistry = BeanPostProcessorRegistry()

    def _init_configuration(self, config_path: Optional[str], packages_to_scan: Optional[list[str]]) -> None:
        """初始化配置参数"""
        # 保存配置路径参数
        self._config_path = config_path

        # 扫描配置
        self._packages_to_scan = packages_to_scan or []

        # Actuator集成器(延迟初始化)
        self._actuator_integration = None

    def _init_smart_features(self, auto_detect: bool) -> None:
        """初始化智能功能"""
        # 智能功能集成
        self._auto_detect = auto_detect
        self._runtime_detector = RuntimeDetector()
        self._is_async_mode = False
        self._async_mode_detected = False

    def _create_smart_bean_factory(self) -> BeanFactory:
        """创建智能Bean工厂

        根据检测到的环境创建合适的Bean工厂实例.
        """
        if self._bean_factory is not None:
            return self._bean_factory

        # 创建基础Bean工厂
        base_factory = DefaultBeanFactory(self._bean_registry, self._dependency_graph)

        # 设置应用上下文引用，以支持 ApplicationContextAware 接口
        base_factory.application_context = self

        # 如果需要智能功能,可以在这里包装
        # 目前直接返回基础工厂,因为智能功能已经集成到应用上下文层面
        self._bean_factory = base_factory
        logger.debug("Smart bean factory created with application context reference")
        return self._bean_factory

    def _get_async_lock(self) -> asyncio.Lock:
        """获取异步锁,如果不存在则创建"""
        if self._async_lock is None:
            self._async_lock = asyncio.Lock()
        return self._async_lock

    async def start(self) -> None:
        """启动应用上下文

        启动过程分为三个阶段:
        1. 准备阶段:环境检测、状态检查、锁获取
        2. 执行阶段:14个启动步骤的核心逻辑
        3. 完成阶段:状态设置、日志记录、异常处理
        """
        await self._prepare_startup()

        async with self._get_async_lock():
            # 双重检查模式
            if self._state == ApplicationContextState.RUNNING:
                logger.debug("Application context is already running (double-check), skipping start")
                return

            if self._state == ApplicationContextState.STARTING:
                logger.debug("Application context is already starting, skipping start")
                return

            try:
                await self._execute_startup_sequence()
                await self._finalize_startup()
            except Exception as e:
                await self._handle_startup_failure(e)

    async def _prepare_startup(self) -> None:
        """准备启动阶段

        执行启动前的准备工作:
        - 智能环境检测
        - 状态检查
        """
        # 智能环境检测
        if self._auto_detect and not self._async_mode_detected:
            self._is_async_mode = self._runtime_detector.is_async_environment()
            self._async_mode_detected = True
            logger.debug(f"Detected async environment: {self._is_async_mode}")

        # 快速检查,避免不必要的锁竞争
        if self._state == ApplicationContextState.RUNNING:
            logger.debug("Application context is already running, skipping start")
            return

    async def _execute_startup_sequence(self) -> None:
        """执行启动序列

        执行14个核心启动步骤
        """
        self._state = ApplicationContextState.STARTING  # 标记启动中
        logger.info("Starting application context...")

        # 1. 初始化异常处理器（延迟初始化）
        await self._initialize_exception_handlers()

        # 2. 初始化环境配置(包含配置文件加载)
        await self._initialize_environment()

        # 3. 初始化智能Bean工厂
        self._create_smart_bean_factory()

        # 4. 初始化日志系统
        await self._initialize_logging()

        # 5. 重新初始化事件发布器(如果已关闭)
        await self._initialize_event_publisher()

        # 6. 扫描和注册组件
        await self._scan_and_register_components()

        # 7. 注册Bean后置处理器
        await self._register_post_processors()

        # 8. 创建单例Bean
        await self._create_singleton_beans()

        # 9. 执行自动配置(在模块初始化之前)
        await self._execute_auto_configurations()

        # 10. 显示启动Banner (在Web服务器启动前显示,避免输出被覆盖)
        await self._display_banner()

        # 11. 初始化模块(条件化初始化)
        await self._initialize_modules()

        # 12. 启动生命周期组件(包括Web服务器)
        await self._start_lifecycle_components()

        # 13. 初始化健康检查集成
        await self._initialize_health_integration()

        # 14. 发布启动完成事件
        await self._publish_startup_event()

        # 15. 记录启动完成信息
        await self._log_startup_info()

    async def _finalize_startup(self) -> None:
        """完成启动阶段

        设置最终状态并记录成功信息
        """
        import time

        self._startup_time = time.time()
        self._state = ApplicationContextState.RUNNING
        logger.info("✅ Application context started successfully")

    async def _handle_startup_failure(self, error: Exception) -> None:
        """处理启动失败

        Args:
            error: 启动过程中发生的异常
        """
        logger.error(f"Failed to start application context: {error}")
        # 启动失败时进行回滚清理
        await self._rollback_startup(error)
        self._state = ApplicationContextState.STOPPED  # 启动失败,回到停止状态

        # 创建启动错误异常
        startup_error = ContextStartupError(f"Failed to start application context: {error}")
        raise startup_error from error

    async def _initialize_exception_handlers(self) -> None:
        """初始化异常处理器（延迟初始化）"""
        logger.debug("Initializing exception handlers...")
        try:
            from miniboot.errors import ensure_handlers_initialized

            ensure_handlers_initialized()
            logger.debug("Exception handlers initialized successfully")
        except Exception as e:
            logger.warning(f"Failed to initialize exception handlers: {e}")
            # 异常处理器初始化失败不应该阻止应用启动

    async def start_with_monitoring(self, main_app=None) -> None:
        """启动应用上下文并自动集成监控 - 接口驱动版本

        Args:
            main_app: 可选的外部应用
        """
        # 先启动核心应用上下文
        await self.start()

        # 然后尝试集成监控组件（如果存在）
        try:
            from miniboot.monitoring.integration import MonitoringIntegration

            monitoring_integration = MonitoringIntegration(self)
            success = await monitoring_integration.auto_integrate_monitoring()

            if success:
                # 保存集成器引用以便后续管理
                self._monitoring_integration = monitoring_integration
                logger.info("✅ Application started with monitoring integration")
            else:
                logger.info("✅ Application started without monitoring (no components found)")

        except ImportError:
            logger.debug("Monitoring integration not available, starting without monitoring")
        except Exception as e:
            logger.warning(f"Failed to integrate monitoring: {e}")
            logger.info("Application will continue without monitoring integration")

    # 保持向后兼容性
    async def start_with_actuator(self, main_app=None) -> None:
        """启动应用上下文并自动集成Actuator - 向后兼容方法

        @deprecated: 使用 start_with_monitoring 替代
        """
        logger.warning("start_with_actuator is deprecated, use start_with_monitoring instead")
        await self.start_with_monitoring(main_app)

    def get_actuator_integration_info(self) -> dict:
        """获取Actuator集成信息"""
        if not self._actuator_integration:
            return {"integrated": False, "reason": "Integration not initialized"}
        return self._actuator_integration.get_integration_info()

    def is_actuator_integrated(self) -> bool:
        """检查Actuator是否已集成"""
        return self._actuator_integration and self._actuator_integration.is_integrated()

    async def shutdown_actuator(self) -> None:
        """关闭Actuator集成"""
        if self._actuator_integration:
            await self._actuator_integration.shutdown_actuator()

    async def stop(self, timeout: float = 30.0) -> None:
        """停止应用上下文

        Args:
            timeout: 停止超时时间(秒),默认30秒
        """
        # 快速检查
        if self._state != ApplicationContextState.RUNNING:
            logger.debug("Application context is not running, skipping stop")
            return

        # 使用异步锁确保线程安全
        async with self._get_async_lock():
            # 双重检查模式
            if self._state != ApplicationContextState.RUNNING:
                logger.debug("Application context is not running, skipping stop")
                return

            if self._state == ApplicationContextState.STOPPING:
                logger.debug("Application context is already stopping, skipping stop")
                return

            try:
                self._state = ApplicationContextState.STOPPING
                logger.info("Stopping application context...")

                # 使用超时控制整个停止流程
                await asyncio.wait_for(self._execute_shutdown_sequence(), timeout=timeout)

                self._state = ApplicationContextState.STOPPED
                logger.info("Application context stopped successfully")

            except asyncio.TimeoutError:
                logger.error(f"Application shutdown timed out after {timeout} seconds, forcing shutdown")
                await self._force_shutdown()
                self._state = ApplicationContextState.STOPPED
                raise ContextShutdownError(f"Application shutdown timed out after {timeout} seconds") from None
            except Exception as e:
                logger.error(f"Failed to stop application context: {e}")
                await self._force_shutdown()
                self._state = ApplicationContextState.STOPPED
                raise ContextShutdownError("Application context shutdown failed", cause=e) from e

    def is_running(self) -> bool:
        """检查应用上下文是否处于运行状态"""
        return self._state == ApplicationContextState.RUNNING

    def get_state(self) -> ApplicationContextState:
        """获取当前应用上下文状态"""
        return self._state

    def is_starting(self) -> bool:
        """检查应用上下文是否正在启动"""
        return self._state == ApplicationContextState.STARTING

    def is_stopping(self) -> bool:
        """检查应用上下文是否正在停止"""
        return self._state == ApplicationContextState.STOPPING

    def is_stopped(self) -> bool:
        """检查应用上下文是否已停止"""
        return self._state == ApplicationContextState.STOPPED

    def register_type(self, cls: type[T], name: Optional[str] = None) -> None:
        """注册Bean类型到容器中"""
        try:
            bean_name = name or cls.__name__.lower()
            bean_definition = BeanDefinition(bean_name=bean_name, bean_class=cls)
            self._bean_registry.register_bean_definition(bean_name, bean_definition)
            logger.debug(f"Registered bean type {cls.__name__} with name '{bean_name}'")
        except Exception as e:
            raise BeanCreationError(name or cls.__name__, f"Failed to register bean type: {e}", e) from e

    def register_singleton(self, name: str, instance: Any) -> None:
        """注册单例Bean实例

        Args:
            name: Bean名称
            instance: Bean实例
        """
        try:
            self._bean_factory.register_singleton(name, instance)
            logger.debug(f"Registered singleton bean '{name}' of type {type(instance).__name__}")
        except Exception as e:
            raise BeanCreationError(name, f"Failed to register singleton bean: {e}", e) from e

    def get_bean_names(self) -> list[str]:
        """获取所有Bean名称

        Returns:
            list[str]: Bean名称列表
        """
        try:
            # 使用Bean工厂的names()方法，它会合并Bean定义和单例Bean的名称
            return self._bean_factory.names()
        except Exception as e:
            logger.warning(f"Failed to get bean names: {e}")
            return []

    def get_bean(self, name: str) -> Any:
        """根据名称获取Bean实例"""
        try:
            return self._bean_factory.get_bean(name)
        except Exception as e:
            if "NoSuchBeanDefinitionError" in str(type(e)):
                raise BeanNotFoundError(name) from e
            raise BeanCreationError(name, f"Failed to get bean: {e}", e) from e

    def get_bean_by_type(self, cls: type[T]) -> Optional[T]:
        """根据类型获取Bean实例"""
        try:
            return self._bean_factory.get_bean_by_type(cls)
        except Exception as e:
            if "NoSuchBeanDefinitionError" in str(type(e)):
                return None
            raise BeanCreationError(cls.__name__, f"Failed to get bean by type: {e}", e) from e

    def get_beans_by_type(self, cls: type[T]) -> dict[str, T]:
        """根据类型获取所有Bean实例"""
        try:
            return self._bean_factory.get_beans_by_type(cls)
        except Exception as e:
            raise BeanCreationError(cls.__name__, f"Failed to get beans by type: {e}", e) from e

    def get_beans_of_type(self, cls: type[T]) -> dict[str, T]:
        """根据类型获取所有Bean实例 - 兼容性方法

        这是为了兼容监控组件发现机制而添加的方法，
        内部调用 get_beans_by_type 方法。
        """
        return self.get_beans_by_type(cls)

    def contains_bean(self, name: str) -> bool:
        """检查是否包含指定名称的Bean"""
        return self._bean_registry.contains_bean_definition(name)

    def get_property(self, key: str, default: Any = None) -> Any:
        """获取配置属性"""
        try:
            return self._environment.get_property(key, default)
        except Exception as e:
            if default is not None:
                return default
            raise PropertyNotFoundError(key) from e

    def get_property_as(self, key: str, target_type: type, default: Any = None) -> Any:
        """获取指定类型的配置属性

        Args:
            key: 属性键
            target_type: 目标类型
            default: 默认值

        Returns:
            转换后的属性值
        """
        try:
            # 延迟导入避免循环依赖
            from miniboot.env.resolver import PropertyResolver

            resolver = PropertyResolver(self._environment)
            return resolver.get_as(key, target_type, default)
        except Exception as e:
            if default is not None:
                return default
            raise PropertyNotFoundError(key) from e

    def publish_event(self, event: Any) -> None:
        """发布事件(同步)"""
        try:
            self._event_publisher.publish_event(event)
        except Exception as e:
            raise EventPublishError(str(type(event)), f"Failed to publish event: {e}", e) from e

    async def publish_event_async(self, event: Any) -> None:
        """发布事件(异步)"""
        try:
            await self._event_publisher.publish_event_async(event)
        except Exception as e:
            raise EventPublishError(str(type(event)), f"Failed to publish async event: {e}", e) from e

    def add_event_listener(
        self, event_type_or_handler, handler=None, order: int = 0, async_exec: bool = False, condition: Optional[str] = None
    ) -> str:
        """添加事件监听器

        支持两种使用方式:
        1. add_event_listener(event_type, handler_function)
        2. add_event_listener(handler_function)  # 自动推断事件类型

        Args:
            event_type_or_handler: 事件类型或处理器函数
            handler: 处理器函数(当第一个参数是事件类型时)
            order: 执行顺序
            async_exec: 是否异步执行
            condition: 执行条件

        Returns:
            str: 监听器ID
        """
        try:
            if handler is None:
                # 第一个参数是处理器函数,需要自动推断事件类型
                handler_func = event_type_or_handler
                # 简单实现:使用通用事件类型
                from miniboot.events.base import Event

                event_type = Event
            else:
                # 第一个参数是事件类型,第二个参数是处理器函数
                event_type = event_type_or_handler
                handler_func = handler

            return self._event_publisher.register_listener(
                event_type=event_type, handler=handler_func, order=order, async_exec=async_exec, condition=condition
            )
        except Exception as e:
            raise EventPublishError("listener_registration", f"Failed to register event listener: {e}", e) from e

    def remove_event_listener(self, listener_id: str) -> bool:
        """移除事件监听器

        Args:
            listener_id: 监听器ID

        Returns:
            bool: 是否成功移除
        """
        try:
            return self._event_publisher.unregister_listener(listener_id)
        except Exception as e:
            logger.warning(f"Failed to remove event listener {listener_id}: {e}")
            return False

    def get_environment(self) -> Environment:
        """获取环境配置对象"""
        return self._environment

    def get_bean_factory(self) -> BeanFactory:
        """获取Bean工厂"""
        if self._bean_factory is None:
            self._create_smart_bean_factory()
        return self._bean_factory

    def refresh(self) -> None:
        """刷新应用上下文"""
        try:
            logger.info("Refreshing application context...")
            # 重新加载环境配置
            self._environment.refresh()
            # 清除Bean缓存并重新创建
            if hasattr(self._bean_factory, "clear_cache"):
                self._bean_factory.clear_cache()
            logger.info("Application context refreshed successfully")
        except Exception as e:
            logger.error(f"Failed to refresh application context: {e}")
            raise ApplicationContextError(f"Refresh failed: {e}", e) from e

    def close(self) -> None:
        """关闭上下文"""
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                asyncio.create_task(self.stop())
            else:
                loop.run_until_complete(self.stop())
        except Exception as e:
            logger.error(f"Failed to close application context: {e}")
            raise ContextShutdownError("Failed to close application context", cause=e) from e

    # 私有辅助方法
    async def _initialize_logging(self) -> None:
        """初始化日志系统"""
        logger.debug("Initializing logging system...")
        try:
            # 简化的日志初始化 - 暂时跳过复杂的配置
            logger.debug("Using default logging configuration")

            # 注册基本的日志配置到上下文（如果Bean工厂已初始化）
            if self._bean_factory:
                # 创建一个简单的日志配置对象
                simple_logging_config = {
                    "level": self._environment.get_property("miniboot.logging.level", "INFO"),
                    "console_enabled": True,
                    "file_enabled": False,
                }
                self.register_singleton("loggingProperties", simple_logging_config)
                logger.debug("Registered simple logging configuration")
            else:
                logger.debug("Bean factory not yet initialized, skipping logging properties registration")

            logger.debug("Logging initialization completed")

        except Exception as e:
            logger.warning(f"Failed to configure logging from config: {e}")
            # 日志配置失败不应该阻止应用启动

    async def _initialize_modules(self) -> None:
        """初始化所有模块(条件化初始化) - 重构版本"""
        logger.info("🚀 Starting enhanced module initialization...")

        try:
            # 导入重构后的统一模块初始化器
            from .module_initializer import UnifiedModuleInitializer

            # 创建模块初始化器
            self._module_initializer = UnifiedModuleInitializer(self)

            # 执行模块初始化
            initialization_result = await self._module_initializer.initialize_all_modules()

            # 记录详细的初始化结果
            self._log_module_initialization_results(initialization_result)

            # 启动已初始化的模块
            await self._start_initialized_modules()

            logger.info("✅ Enhanced module initialization completed")

        except Exception as e:
            logger.error(f"❌ Failed to initialize modules: {e}")
            # 模块初始化失败不应该阻止应用启动,但需要记录错误
            logger.warning("⚠️ Application will continue with limited functionality")

    def _log_module_initialization_results(self, result: dict) -> None:
        """记录模块初始化结果的详细信息"""
        statistics = result.get("statistics", {})

        logger.debug("📊 Module Initialization Summary:")
        logger.debug(
            f"  📦 Total modules: {statistics.get('initialized_count', 0) + statistics.get('failed_count', 0) + statistics.get('disabled_count', 0)}"
        )
        logger.debug(f"  ✅ Initialized: {statistics.get('initialized_count', 0)}")
        logger.debug(f"  ❌ Failed: {statistics.get('failed_count', 0)}")
        logger.debug(f"  🔒 Disabled: {statistics.get('disabled_count', 0)}")
        logger.debug(f"  📈 Success rate: {statistics.get('success_rate', 0):.1%}")
        logger.debug(f"  ⏱️ Total time: {statistics.get('total_initialization_time', 0):.3f}s")

        # 记录具体的模块状态
        initialized_modules = result.get("initialized_modules", set())
        failed_modules = result.get("failed_modules", set())
        disabled_modules = result.get("disabled_modules", set())

        if initialized_modules:
            logger.debug(f"🟢 Initialized modules: {', '.join(sorted(initialized_modules))}")

        if failed_modules:
            logger.warning(f"🔴 Failed modules: {', '.join(sorted(failed_modules))}")

        if disabled_modules:
            logger.debug(f"🟡 Disabled modules: {', '.join(sorted(disabled_modules))}")

    async def _start_initialized_modules(self) -> None:
        """启动已初始化的模块"""
        if hasattr(self, "_module_initializer") and self._module_initializer:
            try:
                logger.debug("🚀 Starting initialized modules...")
                start_results = await self._module_initializer.start_all_modules()

                successful_starts = sum(1 for success in start_results.values() if success)
                total_starts = len(start_results)

                if successful_starts > 0:
                    logger.info(f"✅ Successfully started {successful_starts}/{total_starts} modules")

                    # 记录启动成功的模块
                    started_modules = [name for name, success in start_results.items() if success]
                    if started_modules:
                        logger.debug(f"🏃 Running modules: {', '.join(sorted(started_modules))}")

                if successful_starts < total_starts:
                    failed_starts = [name for name, success in start_results.items() if not success]
                    logger.warning(f"⚠️ Failed to start modules: {', '.join(failed_starts)}")

            except Exception as e:
                logger.error(f"❌ Failed to start modules: {e}")
                logger.warning("⚠️ Modules initialized but may not be fully operational")

    # 新增的模块管理方法
    def get_module_initializer(self):
        """获取模块初始化器实例

        Returns:
            UnifiedModuleInitializer: 模块初始化器实例，如果未初始化则返回None
        """
        return getattr(self, "_module_initializer", None)

    async def restart_module(self, module_name: str) -> bool:
        """重启指定模块

        Args:
            module_name: 模块名称

        Returns:
            bool: 重启是否成功
        """
        if hasattr(self, "_module_initializer") and self._module_initializer:
            try:
                success = await self._module_initializer.restart_module(module_name)
                if success:
                    logger.info(f"✅ Module {module_name} restarted successfully")
                else:
                    logger.warning(f"⚠️ Failed to restart module {module_name}")
                return success
            except Exception as e:
                logger.error(f"❌ Error restarting module {module_name}: {e}")
                return False
        else:
            logger.warning("Module initializer not available")
            return False

    async def stop_module(self, module_name: str) -> bool:
        """停止指定模块

        Args:
            module_name: 模块名称

        Returns:
            bool: 停止是否成功
        """
        if hasattr(self, "_module_initializer") and self._module_initializer:
            try:
                success = await self._module_initializer.module_initializer.stop_module(module_name)
                if success:
                    logger.info(f"🛑 Module {module_name} stopped successfully")
                else:
                    logger.warning(f"⚠️ Failed to stop module {module_name}")
                return success
            except Exception as e:
                logger.error(f"❌ Error stopping module {module_name}: {e}")
                return False
        else:
            logger.warning("Module initializer not available")
            return False

    async def start_module(self, module_name: str) -> bool:
        """启动指定模块

        Args:
            module_name: 模块名称

        Returns:
            bool: 启动是否成功
        """
        if hasattr(self, "_module_initializer") and self._module_initializer:
            try:
                success = await self._module_initializer.module_initializer.start_module(module_name)
                if success:
                    logger.info(f"🚀 Module {module_name} started successfully")
                else:
                    logger.warning(f"⚠️ Failed to start module {module_name}")
                return success
            except Exception as e:
                logger.error(f"❌ Error starting module {module_name}: {e}")
                return False
        else:
            logger.warning("Module initializer not available")
            return False

    async def check_module_health(self, module_name: str) -> dict:
        """检查模块健康状态

        Args:
            module_name: 模块名称

        Returns:
            dict: 健康状态信息
        """
        if hasattr(self, "_module_initializer") and self._module_initializer:
            try:
                health_info = await self._module_initializer.check_module_health(module_name)
                return health_info
            except Exception as e:
                logger.error(f"❌ Error checking module {module_name} health: {e}")
                return {"status": "error", "message": str(e)}
        else:
            return {"status": "unavailable", "message": "Module initializer not available"}

    def get_module_status(self) -> dict:
        """获取所有模块状态

        Returns:
            dict: 模块状态信息
        """
        if hasattr(self, "_module_initializer") and self._module_initializer:
            try:
                return self._module_initializer.get_module_status()
            except Exception as e:
                logger.error(f"❌ Error getting module status: {e}")
                return {"error": str(e)}
        else:
            return {"error": "Module initializer not available"}

    def get_application_status(self) -> dict:
        """获取应用上下文的详细状态信息

        Returns:
            dict: 包含应用状态、模块状态、组件状态等的详细信息
        """
        status = {
            "application": {
                "state": self._state.value,
                "is_running": self.is_running(),
                "is_starting": self.is_starting(),
                "is_stopping": self.is_stopping(),
                "startup_time": getattr(self, "_startup_time", None),
                "uptime": self._calculate_uptime() if self.is_running() else None,
            },
            "environment": {
                "active_profiles": self._environment.get_active_profiles() if self._environment else [],
                "config_path": getattr(self, "_config_path", None),
                "packages_to_scan": getattr(self, "_packages_to_scan", []),
            },
            "components": {
                "bean_factory_available": self._bean_factory is not None,
                "event_publisher_available": self._event_publisher is not None,
                "module_initializer_available": hasattr(self, "_module_initializer") and self._module_initializer is not None,
                "actuator_integration_available": getattr(self, "_actuator_integration", None) is not None,
            },
            "modules": self.get_module_status(),
            "beans": {
                "total_registered": len(self._bean_registry.get_bean_definition_names()) if self._bean_registry else 0,
                "singleton_count": len(self._bean_factory.get_singleton_names()) if self._bean_factory else 0,
            },
        }

        # 添加运行时检测信息
        if hasattr(self, "_runtime_detector") and self._runtime_detector:
            status["runtime"] = {
                "async_mode": getattr(self, "_is_async_mode", False),
                "async_mode_detected": getattr(self, "_async_mode_detected", False),
                "auto_detect_enabled": getattr(self, "_auto_detect", False),
            }

        return status

    def _calculate_uptime(self) -> float:
        """计算应用运行时间（秒）"""
        if hasattr(self, "_startup_time") and self._startup_time:
            import time

            return time.time() - self._startup_time
        return 0.0

    async def _initialize_async_module(self) -> None:
        """初始化异步模块(如果启用)- 已废弃,由统一模块初始化器处理"""
        logger.debug("Async module initialization is now handled by UnifiedModuleInitializer")
        # 这个方法保留是为了向后兼容,实际初始化已经移到统一模块初始化器中

    async def _log_async_module_status(self) -> None:
        """记录异步模块状态信息"""
        try:
            # 检查异步模块是否已集成（通过检查Bean工厂中的异步处理器）
            bean_factory = self.get_bean_factory()
            async_enabled = hasattr(bean_factory, "_post_processors") and any(
                "async" in str(type(processor)).lower() for processor in getattr(bean_factory, "_post_processors", [])
            )

            if async_enabled:
                logger.debug("🔄 Async Module: Enabled")
                logger.debug("🏊 Thread Pools: Lazy loading mode (created on demand)")
            else:
                logger.debug("🔄 Async Module: Disabled")

        except Exception as e:
            logger.debug(f"Failed to log async module status: {e}")

    async def _stop_all_modules(self) -> None:
        """停止所有模块(统一模块停止) - 重构版本"""
        logger.info("🛑 Stopping all modules...")

        try:
            # 检查是否有重构后的模块初始化器
            if hasattr(self, "_module_initializer") and self._module_initializer:
                logger.debug("Using enhanced module initializer for shutdown")

                # 获取当前运行的模块状态
                module_status = self._module_initializer.get_module_status()
                running_modules = module_status.get("running_modules", set())

                if running_modules:
                    logger.debug(f"🔄 Stopping {len(running_modules)} running modules...")

                    # 停止所有运行中的模块
                    stop_results = await self._module_initializer.stop_all_modules()

                    # 记录停止结果
                    successful_stops = sum(1 for success in stop_results.values() if success)
                    total_stops = len(stop_results)

                    if successful_stops > 0:
                        logger.info(f"✅ Successfully stopped {successful_stops}/{total_stops} modules")

                        # 记录停止成功的模块
                        stopped_modules = [name for name, success in stop_results.items() if success]
                        if stopped_modules:
                            logger.debug(f"🛑 Stopped modules: {', '.join(sorted(stopped_modules))}")

                    if successful_stops < total_stops:
                        failed_stops = [name for name, success in stop_results.items() if not success]
                        logger.warning(f"⚠️ Failed to stop modules: {', '.join(failed_stops)}")
                else:
                    logger.info("ℹ️ No running modules to stop")

                logger.info("✅ Enhanced module shutdown completed")
            else:
                # 回退到传统的停止方式
                logger.warning("⚠️ Module initializer not found, using traditional stop methods")
                await self._stop_scheduled_tasks()
                await self._stop_web_server()

        except Exception as e:
            logger.error(f"❌ Failed to stop modules: {e}")
            # 尝试传统的停止方式作为备用
            try:
                logger.info("🔄 Attempting fallback stop methods...")
                await self._stop_scheduled_tasks()
                await self._stop_web_server()
                logger.info("✅ Fallback stop methods completed")
            except Exception as fallback_error:
                logger.error(f"❌ Fallback stop methods also failed: {fallback_error}")

    async def _display_banner(self) -> None:
        """显示启动Banner"""
        logger.debug("Displaying startup banner...")
        try:
            # 检查是否禁用Banner显示
            banner_enabled = self._environment.get_property("miniboot.banner.enabled", True)
            logger.debug(f"Banner enabled: {banner_enabled}")
            if banner_enabled:
                # 使用Banner模块显示启动信息
                from miniboot.banner.banner import BannerPrinter

                # 获取应用信息
                app_name = self._environment.get_property("miniboot.application.name", "Mini-Boot Application")
                app_version = self._environment.get_property("miniboot.application.version", "1.0.0")
                logger.debug(f"Banner app info: {app_name} v{app_version}")

                # 从环境配置打印横幅
                logger.debug("Calling BannerPrinter.print_from_env...")
                BannerPrinter.print_from_env(self._environment, app_name, app_version)
                logger.debug("Banner printing completed")
            else:
                logger.debug("Banner is disabled")
        except Exception as e:
            logger.warning(f"Failed to display banner: {e}")
            import traceback

            logger.debug(f"Banner error traceback: {traceback.format_exc()}")

    async def _initialize_event_publisher(self) -> None:
        """初始化事件发布器"""
        logger.debug("Initializing event publisher...")
        try:
            # 检查事件发布器是否已关闭
            if hasattr(self._event_publisher, "_publisher") and hasattr(self._event_publisher._publisher, "_shutdown"):
                if self._event_publisher._publisher._shutdown:
                    logger.debug("Event publisher was shutdown, creating new instance")
                    from miniboot.events.publisher import ApplicationEventPublisher

                    self._event_publisher = ApplicationEventPublisher()
                    logger.debug("New event publisher created")
                else:
                    logger.debug("Event publisher is active, no need to recreate")
            else:
                logger.debug("Event publisher status unknown, ensuring it's active")

        except Exception as e:
            logger.warning(f"Failed to initialize event publisher: {e}")

    async def _initialize_environment(self) -> None:
        """初始化环境配置"""
        logger.debug("Initializing environment...")

        # 创建标准环境实例
        self._environment = StandardEnvironment()

        # 处理配置文件路径
        # 优先使用传入的config_path,其次使用环境变量MINIBOOT_CONFIG_PATH
        config_path = self._config_path or os.environ.get("MINIBOOT_CONFIG_PATH")
        if config_path:
            logger.debug(f"Using config path: {config_path}")
        else:
            logger.debug("No specific config path provided, will use default search locations")

        # 加载配置文件到环境中
        try:
            from miniboot.env.config import ConfigurationLoader

            # 获取当前激活的profiles
            active_profiles = self._environment.get_active_profiles()
            logger.debug(f"Active profiles: {list(active_profiles)}")

            # 创建配置加载器
            if config_path:
                config_path_obj = Path(config_path)
                if config_path_obj.is_file() or config_path_obj.suffix in [".yml", ".yaml", ".json", ".properties"]:
                    # 自定义文件：使用文件所在目录作为搜索位置，并添加自定义文件
                    search_locations = [str(config_path_obj.parent)]
                    config_loader = ConfigurationLoader(search_locations)
                    config_loader.add_custom_config_file(config_path_obj, priority=1500)
                    logger.debug(f"Added custom config file: {config_path_obj} with high priority")
                else:
                    # 自定义目录：使用该目录作为搜索位置
                    search_locations = [str(config_path)]
                    config_loader = ConfigurationLoader(search_locations)
                    logger.debug(f"Using custom config directory: {config_path}")
            else:
                # 使用默认搜索路径
                search_locations = ["resources/", "config/", "./"]
                config_loader = ConfigurationLoader(search_locations)

            # 统一的配置加载流程
            property_sources = self._environment.get_property_sources()
            load_result = config_loader.load_configuration(property_sources, active_profiles)

            # 记录加载结果
            logger.debug(f"Configuration loading completed in {load_result.load_time_ms:.2f}ms")
            logger.debug(f"Loaded {len(load_result.loaded_files)} files, {len(load_result.failed_files)} failed")

            # 重新配置 profiles,因为配置文件可能包含 profile 设置
            self._environment._setup_profiles()

            logger.debug("Configuration files loaded to environment successfully")

        except Exception as e:
            logger.warning(f"Failed to load configuration files to environment: {e}")
            # 配置加载失败不应该阻止应用启动,使用默认配置继续

        logger.debug("Environment initialization completed")

    async def _load_configuration(self) -> None:
        """加载配置文件(已在环境初始化时完成)"""
        logger.debug("Configuration already loaded during environment initialization")
        # 配置文件加载已经在 _initialize_environment 阶段完成
        # 这个方法保留用于向后兼容或额外的配置处理

    async def _scan_and_register_components(self) -> None:
        """扫描和注册组件"""
        logger.debug("Scanning and registering components...")
        try:
            if self._packages_to_scan:
                logger.debug(f"Scanning packages: {self._packages_to_scan}")
                for package in self._packages_to_scan:
                    try:
                        # 使用组件扫描器扫描包
                        scan_result = self._component_scanner.scan(package)
                        components = scan_result.components
                        logger.debug(f"Found {len(components)} components in package {package}")
                        for _, component_class in components.items():
                            self.register_type(component_class)
                    except Exception as e:
                        logger.debug(f"Failed to scan package {package}: {e}")
            else:
                logger.debug("No packages specified for scanning")
        except Exception as e:
            logger.error(f"Component scanning failed: {e}")
            raise

    async def _register_post_processors(self) -> None:
        """注册框架级和用户自定义的Bean后置处理器

        注册策略(参考Spring Boot):
        1. Bean工厂已注册核心处理器(依赖注入、值注入、基础生命周期)
        2. ApplicationContext注册框架特性处理器(事件、定时任务、配置属性)
        3. ApplicationContext注册用户自定义处理器
        """
        logger.debug("Registering framework and custom post processors...")
        try:
            # 检查Bean工厂是否支持后置处理器
            if not hasattr(self._bean_factory, "add_processor"):
                logger.debug("Bean factory does not support post processors")
                return

            # 获取当前处理器数量
            current_processor_count = len(getattr(self._bean_factory, "_post_processors", []))
            logger.debug(f"Bean factory has {current_processor_count} existing post processors")

            # 异步注册框架级后置处理器
            framework_processors = await self._get_framework_processors()
            framework_count = await self._register_processors_direct(framework_processors, "framework")

            # 异步注册用户自定义后置处理器
            custom_processors = await self._get_custom_processors()
            custom_count = await self._register_processors_direct(custom_processors, "custom")

            total_processors = len(getattr(self._bean_factory, "_post_processors", []))
            logger.debug(
                f"Post processor registration completed: {total_processors} total "
                f"({current_processor_count} existing + {framework_count} framework + {custom_count} custom)"
            )

        except Exception as e:
            logger.debug(f"Post processor registration completed with warnings: {e}")

    async def _register_processors_async(self, processor_registry, processors: list, processor_type: str) -> int:
        """异步注册处理器列表

        Args:
            processor_registry: 处理器注册表
            processors: 处理器列表
            processor_type: 处理器类型(用于日志)

        Returns:
            int: 成功注册的处理器数量
        """
        registered_count = 0

        for i, processor in enumerate(processors):
            try:
                # 每3个处理器让出一次控制权,减少异步开销
                if i > 0 and i % 3 == 0:
                    await asyncio.sleep(0)  # 仅让出控制权,不添加延迟

                processor_registry.register_processor(processor)
                registered_count += 1
                logger.debug(f"Registered {processor_type} post processor: {processor.__class__.__name__}")

            except Exception as e:
                logger.debug(f"Failed to register {processor_type} processor {processor.__class__.__name__}: {e}")

        return registered_count

    async def _register_processors_direct(self, processors: list, processor_type: str) -> int:
        """直接注册处理器到Bean工厂

        Args:
            processors: 处理器列表
            processor_type: 处理器类型(用于日志)

        Returns:
            int: 成功注册的处理器数量
        """
        registered_count = 0

        for i, processor in enumerate(processors):
            try:
                # 每3个处理器让出一次控制权,减少异步开销
                if i > 0 and i % 3 == 0:
                    await asyncio.sleep(0)  # 仅让出控制权,不添加延迟

                self._bean_factory.add_processor(processor)
                registered_count += 1
                logger.debug(f"Registered {processor_type} post processor: {processor.__class__.__name__}")

            except Exception as e:
                logger.debug(f"Failed to register {processor_type} processor {processor.__class__.__name__}: {e}")

        return registered_count

    async def _get_framework_processors(self) -> list:
        """获取框架级后置处理器(异步版本)

        这些处理器提供Mini-Boot框架的特性功能,包括:
        - 事件监听处理器
        - 定时任务处理器
        - 配置属性处理器

        Returns:
            list: 框架级后置处理器列表
        """
        framework_processors = []

        # 使用异步方式加载处理器,避免阻塞事件循环
        try:
            # 在异步上下文中执行导入,允许事件循环处理其他任务
            await asyncio.sleep(0)  # 让出控制权

            # 延迟导入避免循环导入
            from ..processor.configuration import ConfigurationPropertiesProcessor
            from ..processor.event import EventListenerProcessor
            from ..processor.lifecycle import LifecycleAnnotationProcessor
            from ..processor.schedule import ScheduledAnnotationProcessor

            # 配置属性处理器(高优先级)
            await asyncio.sleep(0)
            config_processor = ConfigurationPropertiesProcessor()
            framework_processors.append(config_processor)
            logger.debug("Loaded framework processor: ConfigurationPropertiesProcessor")

            # 生命周期注解处理器(核心功能,高优先级)
            await asyncio.sleep(0)
            lifecycle_processor = LifecycleAnnotationProcessor()
            framework_processors.append(lifecycle_processor)
            logger.debug("Loaded framework processor: LifecycleAnnotationProcessor")

            # 事件监听处理器
            await asyncio.sleep(0)
            event_processor = EventListenerProcessor()
            framework_processors.append(event_processor)
            logger.debug("Loaded framework processor: EventListenerProcessor")

            # 定时任务处理器(低优先级)
            await asyncio.sleep(0)
            scheduled_processor = ScheduledAnnotationProcessor()
            framework_processors.append(scheduled_processor)
            logger.debug("Loaded framework processor: ScheduledAnnotationProcessor")

        except ImportError as e:
            logger.debug(f"Some framework processors not available: {e}")
        except Exception as e:
            logger.debug(f"Failed to load framework processors: {e}")

        return framework_processors

    async def _get_custom_processors(self) -> list:
        """获取用户自定义后置处理器(异步版本)

        根据配置和环境添加用户自定义的处理器,包括:
        - 性能监控处理器
        - 安全检查处理器
        - 用户扩展处理器

        Returns:
            list: 用户自定义后置处理器列表
        """
        custom_processors = []

        # 异步加载可选处理器

        # 性能监控处理器
        if self.get_property("miniboot.performance.enabled", False):
            try:
                await asyncio.sleep(0)
                from ..processor.performance import PerformanceMonitoringProcessor

                custom_processors.append(PerformanceMonitoringProcessor())
                logger.debug("Loaded optional processor: PerformanceMonitoringProcessor")
            except ImportError:
                logger.debug("Performance monitoring processor not available")
            except Exception as e:
                logger.debug(f"Failed to load performance processor: {e}")

        # 安全检查处理器
        if self.get_property("miniboot.security.enabled", False):
            try:
                await asyncio.sleep(0)
                from ..processor.security import SecurityProcessor

                custom_processors.append(SecurityProcessor())
                logger.debug("Loaded optional processor: SecurityProcessor")
            except ImportError:
                logger.debug("Security processor not available")
            except Exception as e:
                logger.debug(f"Failed to load security processor: {e}")

        # 调试处理器(仅在开发环境)
        if self.get_property("miniboot.debug.enabled", False):
            try:
                await asyncio.sleep(0)
                from ..processor.debug import DebugProcessor

                custom_processors.append(DebugProcessor())
                logger.debug("Loaded optional processor: DebugProcessor")
            except ImportError:
                logger.debug("Debug processor not available")
            except Exception as e:
                logger.debug(f"Failed to load debug processor: {e}")

        # 异步加载用户自定义处理器类
        custom_processor_classes = self.get_property("miniboot.processors.custom", [])
        for processor_class_name in custom_processor_classes:
            try:
                # 让出控制权
                await asyncio.sleep(0)

                # 动态加载用户指定的处理器类
                module_name, class_name = processor_class_name.rsplit(".", 1)
                module = __import__(module_name, fromlist=[class_name])
                processor_class = getattr(module, class_name)
                custom_processors.append(processor_class())
                logger.debug(f"Loaded custom processor: {processor_class_name}")

            except Exception as e:
                logger.warning(f"Failed to load custom processor {processor_class_name}: {e}")

        return custom_processors

    async def _create_singleton_beans(self) -> None:
        """创建单例Bean"""
        logger.debug("Creating singleton beans...")
        try:
            # 检查Bean注册表是否有get_bean_definition_names方法
            if hasattr(self._bean_registry, "get_bean_definition_names"):
                # 获取所有单例Bean定义并创建实例
                bean_names = self._bean_registry.get_bean_definition_names()
                singleton_count = 0

                for bean_name in bean_names:
                    try:
                        bean_definition = self._bean_registry.get_bean_definition(bean_name)
                        if bean_definition.is_singleton() and not bean_definition.lazy_init:
                            # 创建非懒加载的单例Bean
                            self._bean_factory.get_bean(bean_name)
                            singleton_count += 1
                            logger.debug(f"Created singleton bean: {bean_name}")
                    except Exception as e:
                        logger.warning(f"Failed to create singleton bean {bean_name}: {e}")

                logger.debug(f"Created {singleton_count} singleton beans")
            else:
                logger.debug("Bean registry does not support get_bean_definition_names, skipping singleton bean creation")
        except Exception as e:
            logger.error(f"Singleton bean creation failed: {e}")
            raise

    async def _start_lifecycle_components(self) -> None:
        """启动生命周期组件"""
        logger.debug("Starting lifecycle components...")
        try:
            # 检查Bean工厂是否支持按类型获取Bean
            if not hasattr(self._bean_factory, "get_beans_by_type"):
                logger.debug("Bean factory does not support get_beans_by_type, checking individual beans")
                # 尝试从注册表中查找Lifecycle类型的Bean
                started_count = 0
                bean_names = self._bean_registry.get_bean_definition_names()

                for bean_name in bean_names:
                    try:
                        bean_definition = self._bean_registry.get_bean_definition(bean_name)
                        # 检查Bean类是否实现了Lifecycle接口
                        from ..bean.lifecycle import Lifecycle

                        if issubclass(bean_definition.bean_class, Lifecycle):
                            bean = self._bean_factory.get_bean(bean_name)
                            if hasattr(bean, "is_running") and hasattr(bean, "start") and not bean.is_running():
                                bean.start()
                                started_count += 1
                                logger.debug(f"Started lifecycle component: {bean_name}")
                    except Exception as e:
                        logger.debug(f"Failed to start lifecycle component {bean_name}: {e}")

                logger.debug(f"Started {started_count} lifecycle components")
                return

            # 查找并启动实现了Lifecycle接口的组件
            from ..bean.lifecycle import Lifecycle

            lifecycle_beans = self._bean_factory.get_beans_by_type(Lifecycle)
            started_count = 0

            for bean_name, lifecycle_bean in lifecycle_beans.items():
                try:
                    if not lifecycle_bean.is_running():
                        lifecycle_bean.start()
                        started_count += 1
                        logger.debug(f"Started lifecycle component: {bean_name}")
                except Exception as e:
                    logger.debug(f"Failed to start lifecycle component {bean_name}: {e}")

            logger.debug(f"Started {started_count} lifecycle components")
        except Exception as e:
            logger.debug(f"Lifecycle component startup completed with warnings: {e}")

    async def _execute_auto_configurations(self) -> None:
        """执行自动配置

        根据配置文件中的设置加载和执行自动配置.
        支持新的基于 AutoConfiguration 基类的自动配置系统.
        """
        try:
            logger.info("🔧 _execute_auto_configurations: 开始执行自动配置...")

            # 检查是否启用自动配置
            auto_config_enabled = self._environment.get_property("miniboot.autoconfigure.enabled", True)
            if not auto_config_enabled:
                logger.debug("Auto configuration is disabled in configuration")
                return

            # 优先使用新的自动配置系统
            try:
                logger.info("🔧 _execute_auto_configurations: 尝试使用新的自动配置系统...")
                await self._execute_new_auto_configurations()
                logger.info("🔧 _execute_auto_configurations: 新的自动配置系统执行成功")
                return
            except ImportError as e:
                logger.info(f"🔧 _execute_auto_configurations: 新的自动配置系统不可用，回退到旧系统: {e}")
            except Exception as e:
                logger.warning(f"🔧 _execute_auto_configurations: 新的自动配置系统失败，回退到旧系统: {e}")

            # 回退到旧的自动配置系统
            logger.info("🔧 _execute_auto_configurations: 使用旧的自动配置系统...")
            await self._execute_legacy_auto_configurations()

        except Exception as e:
            logger.error(f"Auto configuration execution failed: {e}")
            # 不抛出异常,允许应用继续启动

    async def _execute_new_auto_configurations(self) -> None:
        """执行新的自动配置系统 - 先加载自动配置，再进行监控集成"""
        try:
            logger.info("🔧 _execute_new_auto_configurations: 开始执行新的自动配置系统...")
            # 第一步：加载和执行自动配置（包括 Actuator）
            await self._load_and_execute_auto_configurations()

            # 第二步：使用接口驱动的监控集成
            from miniboot.monitoring.integration import MonitoringIntegration

            # 创建监控集成管理器
            monitoring_integration = MonitoringIntegration(self)

            # 自动集成监控组件
            result = await monitoring_integration.auto_integrate_monitoring()

            if result:
                logger.info("✅ Interface-driven monitoring integration completed successfully")
            else:
                logger.debug("No monitoring components found for integration")

        except ImportError as e:
            # 监控模块不可用，这是正常的（可选依赖）
            logger.info(f"🔧 _execute_new_auto_configurations: 监控集成模块不可用: {e}")
            raise  # 重新抛出 ImportError 以便回退到旧系统
        except Exception as e:
            # 监控集成失败不应该影响应用启动
            logger.warning(f"🔧 _execute_new_auto_configurations: 监控集成失败: {e}")
            raise  # 重新抛出异常以便回退到旧系统

    async def _load_and_execute_auto_configurations(self) -> None:
        """加载和执行自动配置（新系统的第一步）"""
        try:
            logger.info("🔧 _load_and_execute_auto_configurations: 开始加载和执行自动配置...")
            # 导入自动配置模块
            from miniboot.autoconfigure import AutoConfigurationLoader

            # 创建加载器
            loader = AutoConfigurationLoader()
            logger.info("🔧 _load_and_execute_auto_configurations: AutoConfigurationLoader 创建成功")

            # 获取排除的配置
            excluded_configs = self._environment.get_property("miniboot.autoconfigure.exclude", [])
            if excluded_configs:
                logger.debug(f"Excluding auto configurations: {excluded_configs}")

            # 获取额外扫描的包
            additional_packages = self._environment.get_property("miniboot.autoconfigure.packages", [])
            if additional_packages:
                logger.debug(f"Additional scan packages: {additional_packages}")

            # 获取starters配置
            starters_config = self._environment.get_property("miniboot.starters", {})
            if starters_config:
                logger.debug(f"Found starters configuration: {list(starters_config.keys())}")
                # 将starters配置注入到环境中,供自动配置使用
                for starter_name, starter_config in starters_config.items():
                    if isinstance(starter_config, dict):
                        for key, value in starter_config.items():
                            property_key = f"{starter_name}.{key}"
                            self._environment.set_property(property_key, value)

            # 加载配置
            discovery = loader.get_discovery()
            logger.info("🔧 _load_and_execute_auto_configurations: discovery 获取成功")

            # 1. 从META-INF/mini.factories文件加载配置
            logger.info("🔧 _load_and_execute_auto_configurations: 开始从 META-INF/mini.factories 文件加载配置...")
            count = self._load_from_factories_files(discovery)
            logger.info(f"🔧 _load_and_execute_auto_configurations: 从 factories 文件加载了 {count} 个配置")

            # 2. 加载额外包中的配置
            if additional_packages:
                additional_count = discovery.load_and_register(packages=additional_packages)
                count += additional_count

            # 获取注册表并移除排除的配置
            registry = loader.get_registry()
            if excluded_configs:
                for config_name in excluded_configs:
                    if config_name in registry._configurations:
                        del registry._configurations[config_name]
                        if config_name in registry._metadata_cache:
                            del registry._metadata_cache[config_name]
                        logger.debug(f"Excluded auto configuration: {config_name}")

            # 执行自动配置
            if count > 0:
                results = registry.configure_all(self)

                # 统计结果
                success_count = sum(1 for success in results.values() if success)
                total_count = len(results)

                logger.debug(f"Auto configuration completed: {success_count}/{total_count} successful")

                # 记录失败的配置
                failed_configs = [name for name, success in results.items() if not success]
                if failed_configs:
                    logger.warning(f"Failed auto configurations: {failed_configs}")
            else:
                logger.debug("No auto configurations found")

        except Exception as e:
            logger.error(f"Auto configuration loading failed: {e}")
            # 不抛出异常,允许应用继续启动

    async def _execute_legacy_auto_configurations(self) -> None:
        """执行旧的自动配置系统（向后兼容）"""
        # 导入自动配置模块
        from miniboot.autoconfigure import AutoConfigurationLoader

        # 创建加载器
        loader = AutoConfigurationLoader()

        # 获取排除的配置
        excluded_configs = self._environment.get_property("miniboot.autoconfigure.exclude", [])
        if excluded_configs:
            logger.debug(f"Excluding auto configurations: {excluded_configs}")

        # 获取额外扫描的包
        additional_packages = self._environment.get_property("miniboot.autoconfigure.packages", [])
        if additional_packages:
            logger.debug(f"Additional scan packages: {additional_packages}")

        # 获取starters配置
        starters_config = self._environment.get_property("miniboot.starters", {})
        if starters_config:
            logger.debug(f"Found starters configuration: {list(starters_config.keys())}")
            # 将starters配置注入到环境中,供自动配置使用
            for starter_name, starter_config in starters_config.items():
                if isinstance(starter_config, dict):
                    for key, value in starter_config.items():
                        property_key = f"{starter_name}.{key}"
                        self._environment.set_property(property_key, value)

        # 加载配置
        discovery = loader.get_discovery()

        # 1. 从META-INF/mini.factories文件加载配置
        count = self._load_from_factories_files(discovery)

        # 2. 加载额外包中的配置
        if additional_packages:
            additional_count = discovery.load_and_register(packages=additional_packages)
            count += additional_count

        # 获取注册表并移除排除的配置
        registry = loader.get_registry()
        if excluded_configs:
            for config_name in excluded_configs:
                if config_name in registry._configurations:
                    del registry._configurations[config_name]
                    if config_name in registry._metadata_cache:
                        del registry._metadata_cache[config_name]
                    logger.debug(f"Excluded auto configuration: {config_name}")

        # 执行自动配置
        if count > 0:
            results = registry.configure_all(self)

            # 统计结果
            success_count = sum(1 for success in results.values() if success)
            total_count = len(results)

            logger.debug(f"Legacy auto configuration completed: {success_count}/{total_count} successful")

            # 记录失败的配置
            failed_configs = [name for name, success in results.items() if not success]
            if failed_configs:
                logger.warning(f"Failed auto configurations: {failed_configs}")
        else:
            logger.debug("No legacy auto configurations found")

    def _load_from_factories_files(self, discovery) -> int:
        """从META-INF/mini.factories文件加载自动配置

        Args:
            discovery: 自动配置发现器

        Returns:
            int: 加载的配置数量
        """
        import os

        logger.info("🔧 _load_from_factories_files: 方法开始执行")
        total_count = 0
        factories_files = []

        try:
            # 获取miniboot.starters包的路径
            import miniboot.starters

            starters_path = os.path.dirname(miniboot.starters.__file__)
            logger.info(f"🔧 _load_from_factories_files: starters_path = {starters_path}")

            # 扫描所有starter目录
            items = os.listdir(starters_path)
            logger.info(f"🔧 _load_from_factories_files: 找到 {len(items)} 个目录项: {items}")

            for item in items:
                starter_dir = os.path.join(starters_path, item)
                logger.info(f"🔧 _load_from_factories_files: 检查目录 {item} -> {starter_dir}")

                if os.path.isdir(starter_dir) and not item.startswith("__"):
                    logger.info(f"🔧 _load_from_factories_files: {item} 是有效的starter目录")
                    factories_file = os.path.join(starter_dir, "META-INF", "mini.factories")
                    logger.info(f"🔧 _load_from_factories_files: 检查factories文件: {factories_file}")

                    if os.path.exists(factories_file):
                        logger.info(f"🔧 _load_from_factories_files: 找到factories文件: {factories_file}")
                        factories_files.append(factories_file)
                    else:
                        logger.info(f"🔧 _load_from_factories_files: factories文件不存在: {factories_file}")
                else:
                    logger.info(f"🔧 _load_from_factories_files: 跳过 {item} (不是目录或以__开头)")

            # 使用discovery的factories_files参数
            if factories_files:
                logger.info(f"🔧 _load_from_factories_files: 找到 {len(factories_files)} 个 factories 文件")
                for f in factories_files:
                    logger.info(f"🔧   - {f}")
                total_count = discovery.load_and_register(factories_files=factories_files)
                logger.info(f"🔧 _load_from_factories_files: 从 {len(factories_files)} 个 factories 文件加载了 {total_count} 个自动配置")
            else:
                logger.warning("🔧 _load_from_factories_files: 没有找到任何 factories 文件")

            return total_count

        except Exception as e:
            logger.error(f"🔧 _load_from_factories_files: 异常发生: {e}")
            import traceback

            logger.error(f"🔧 _load_from_factories_files: 异常堆栈: {traceback.format_exc()}")
            return 0

    async def _publish_startup_event(self) -> None:
        """发布启动完成事件"""
        logger.debug("Publishing startup event...")
        try:
            # 创建并发布应用启动完成事件
            from ..events import ApplicationStartedEvent

            startup_event = ApplicationStartedEvent(
                application=self,
                startup_time=None,  # 可以在这里计算启动时间
            )

            await self.publish_event_async(startup_event)
            logger.debug("Application startup event published")
        except Exception as e:
            logger.debug(f"Startup event publishing completed with warnings: {e}")

    async def _log_startup_info(self) -> None:
        """记录启动完成信息"""
        logger.debug("Logging startup information...")
        try:
            # 记录启动统计信息
            bean_count = len(self._bean_registry.get_bean_definition_names())

            # 获取实际的配置信息
            config_info = self._get_config_display_info()

            # 获取激活的profiles
            if self._environment is not None:
                active_profiles = self._environment.get_active_profiles()
                profile_info = f"[{', '.join(active_profiles)}]" if active_profiles else "[default]"
                env_name = self._environment.__class__.__name__
            else:
                profile_info = "[unknown]"
                env_name = "Not initialized"

            logger.info("=" * 60)
            logger.info("🚀 Mini-Boot Application Started Successfully!")
            logger.info(f"📦 Total Beans: {bean_count}")
            logger.info(f"🌍 Environment: {env_name}")
            logger.info(f"📁 Config Path: {config_info}")
            logger.info(f"🏷️ Active Profiles: {profile_info}")
            logger.info(f"📋 Packages Scanned: {len(self._packages_to_scan)}")
            logger.info("=" * 60)
        except Exception as e:
            logger.warning(f"Failed to log startup info: {e}")

    def _get_config_display_info(self) -> str:
        """获取配置显示信息"""
        try:
            # 如果指定了配置路径,显示指定的路径
            if self._config_path:
                return str(self._config_path)

            # 尝试从配置加载器发现实际加载的配置文件
            from miniboot.env.config_loader import ConfigurationLoader

            active_profiles = self._environment.get_active_profiles()
            config_loader = ConfigurationLoader()
            config_files = config_loader.discover_config_files(active_profiles)

            if config_files:
                # 显示主要的配置文件
                main_configs = [f for f in config_files if "application" in f.name]
                if main_configs:
                    return str(main_configs[0])
                else:
                    return str(config_files[0])

            # 如果没有发现配置文件,检查是否有激活的profiles
            active_profiles = self._environment.get_active_profiles()
            if active_profiles:
                return f"application-{list(active_profiles)[0]}.yml (not found, using defaults)"

            return "application.yml (not found, using defaults)"

        except Exception as e:
            logger.debug(f"Failed to get config display info: {e}")
            return "Default"

    async def _rollback_startup(self, _: Exception) -> None:
        """启动失败时的回滚清理

        Args:
            startup_error: 导致启动失败的异常
        """
        logger.warning("Starting rollback due to startup failure...")
        rollback_errors = []

        try:
            # 1. 停止已启动的生命周期组件
            await self._rollback_lifecycle_components(rollback_errors)

            # 2. 销毁已创建的单例Bean
            await self._rollback_singleton_beans(rollback_errors)

            # 3. 清理Bean后置处理器
            await self._rollback_post_processors(rollback_errors)

            # 4. 清理事件发布器
            await self._rollback_event_publisher(rollback_errors)

            # 5. 重置运行状态
            self._state = ApplicationContextState.STOPPED

            if rollback_errors:
                logger.warning(f"Rollback completed with {len(rollback_errors)} errors")
                for error in rollback_errors:
                    logger.debug(f"Rollback error: {error}")
            else:
                logger.info("Rollback completed successfully")

        except Exception as e:
            logger.error(f"Critical error during rollback: {e}")
            rollback_errors.append(e)

    async def _rollback_lifecycle_components(self, rollback_errors: list) -> None:
        """回滚生命周期组件"""
        try:
            if hasattr(self._bean_factory, "_lifecycle_processor"):
                lifecycle_processor = self._bean_factory._lifecycle_processor
                if lifecycle_processor and lifecycle_processor.is_running():
                    lifecycle_processor.stop()
                    logger.debug("Stopped lifecycle processor during rollback")
        except Exception as e:
            rollback_errors.append(f"Failed to stop lifecycle components: {e}")

    async def _rollback_singleton_beans(self, rollback_errors: list) -> None:
        """回滚单例Bean"""
        try:
            if hasattr(self._bean_factory, "destroy_singletons"):
                self._bean_factory.destroy_singletons()
                logger.debug("Destroyed singleton beans during rollback")
            elif hasattr(self._bean_factory, "_singleton_objects"):
                # 手动清理单例缓存
                singleton_count = len(self._bean_factory._singleton_objects)
                self._bean_factory._singleton_objects.clear()
                logger.debug(f"Cleared {singleton_count} singleton beans during rollback")
        except Exception as e:
            rollback_errors.append(f"Failed to destroy singleton beans: {e}")

    async def _rollback_post_processors(self, rollback_errors: list) -> None:
        """回滚Bean后置处理器"""
        try:
            if hasattr(self._bean_factory, "_post_processor_registry"):
                registry = self._bean_factory._post_processor_registry
                if hasattr(registry, "clear"):
                    registry.clear()
                    logger.debug("Cleared post processor registry during rollback")
        except Exception as e:
            rollback_errors.append(f"Failed to clear post processors: {e}")

    async def _rollback_event_publisher(self, rollback_errors: list) -> None:
        """回滚事件发布器"""
        try:
            if hasattr(self._event_publisher, "shutdown"):
                await self._event_publisher.shutdown()
                logger.debug("Shutdown event publisher during rollback")
        except Exception as e:
            rollback_errors.append(f"Failed to shutdown event publisher: {e}")

    async def _publish_stop_event(self) -> None:
        """发布停止事件"""
        try:
            from miniboot.events.types import ApplicationStoppedEvent

            stop_event = ApplicationStoppedEvent(application=self)
            await self.publish_event_async(stop_event)
            logger.debug("Application stop event published")
        except Exception as e:
            logger.warning(f"Failed to publish stop event: {e}")

    async def _stop_lifecycle_components(self) -> None:
        """停止生命周期组件"""
        try:
            if hasattr(self._bean_factory, "_lifecycle_processor"):
                lifecycle_processor = self._bean_factory._lifecycle_processor
                if lifecycle_processor and lifecycle_processor.is_running():
                    lifecycle_processor.stop()
                    logger.debug("Stopped lifecycle processor")
        except Exception as e:
            logger.warning(f"Failed to stop lifecycle components: {e}")

    async def _destroy_singleton_beans(self) -> None:
        """销毁单例Bean(按依赖顺序逆序)"""
        try:
            logger.debug("Starting singleton beans destruction...")

            # 首先尝试使用Bean工厂的销毁方法
            if hasattr(self._bean_factory, "destroy"):
                # 在调用Bean工厂销毁方法之前，先调用LifecycleAnnotationProcessor处理@PreDestroy方法
                await self._destroy_beans_with_lifecycle_processor()

                # 然后调用Bean工厂的销毁方法处理DisposableBean接口
                self._bean_factory.destroy()
                logger.debug("Destroyed singleton beans using factory method")
                return

            # 如果没有专门的销毁方法,手动按依赖顺序逆序销毁
            await self._destroy_beans_by_dependency_order()

        except Exception as e:
            logger.warning(f"Failed to destroy singleton beans: {e}")

    async def _destroy_beans_with_lifecycle_processor(self) -> None:
        """使用LifecycleAnnotationProcessor销毁Bean，处理@PreDestroy方法"""
        try:
            # 获取LifecycleAnnotationProcessor
            lifecycle_processor = None
            for processor in self._bean_factory.processors():
                if hasattr(processor, "destroy_bean") and hasattr(processor, "_invoke_pre_destroy_methods"):
                    lifecycle_processor = processor
                    break

            if not lifecycle_processor:
                logger.debug("No LifecycleAnnotationProcessor found, skipping @PreDestroy method calls")
                return

            # 获取所有单例Bean名称
            singleton_names = []
            if hasattr(self._bean_factory, "_cache") and hasattr(self._bean_factory._cache, "names"):
                singleton_names = self._bean_factory._cache.names()

            if not singleton_names:
                logger.debug("No singleton beans found for lifecycle destruction")
                return

            logger.debug(f"Destroying {len(singleton_names)} singleton beans with lifecycle processor...")

            # 逆序销毁Bean（后创建的先销毁）
            for bean_name in reversed(singleton_names):
                try:
                    bean_instance = self._bean_factory._cache.get(bean_name)
                    if bean_instance:
                        lifecycle_processor.destroy_bean(bean_instance, bean_name)
                        logger.debug(f"Called lifecycle destruction for bean: {bean_name}")
                except Exception as e:
                    logger.warning(f"Failed to destroy bean '{bean_name}' with lifecycle processor: {e}")

            logger.debug("Lifecycle-based bean destruction completed")

        except Exception as e:
            logger.warning(f"Error during lifecycle-based bean destruction: {e}")

    async def _destroy_beans_by_dependency_order(self) -> None:
        """按依赖顺序逆序销毁Bean"""
        try:
            # 获取所有单例Bean
            singleton_beans = []
            if hasattr(self._bean_factory, "_singleton_objects"):
                singleton_beans = list(self._bean_factory._singleton_objects.items())

            if not singleton_beans:
                logger.debug("No singleton beans to destroy")
                return

            logger.debug(f"Destroying {len(singleton_beans)} singleton beans...")

            # 逆序销毁(后创建的先销毁)
            for bean_name, bean_instance in reversed(singleton_beans):
                try:
                    await self._destroy_single_bean(bean_name, bean_instance)
                except Exception as e:
                    logger.warning(f"Failed to destroy bean '{bean_name}': {e}")

            # 清理单例缓存
            if hasattr(self._bean_factory, "_singleton_objects"):
                self._bean_factory._singleton_objects.clear()

            logger.debug("Singleton beans destruction completed")

        except Exception as e:
            logger.warning(f"Error during beans destruction: {e}")

    async def _destroy_single_bean(self, bean_name: str, bean_instance) -> None:
        """销毁单个Bean

        Args:
            bean_name: Bean名称
            bean_instance: Bean实例
        """
        try:
            # 调用@PreDestroy方法
            if hasattr(bean_instance, "__class__"):
                # 检查类的所有方法
                for method_name in dir(bean_instance.__class__):
                    if not method_name.startswith("_"):  # 跳过私有方法
                        method = getattr(bean_instance.__class__, method_name, None)
                        if callable(method) and hasattr(method, "__is_pre_destroy__"):
                            # 获取实例方法
                            instance_method = getattr(bean_instance, method_name)
                            if asyncio.iscoroutinefunction(instance_method):
                                await instance_method()
                            else:
                                instance_method()
                            logger.debug(f"Called @PreDestroy method '{method_name}' on bean '{bean_name}'")

            # 如果Bean实现了Disposable接口
            if hasattr(bean_instance, "destroy"):
                destroy_method = bean_instance.destroy
                if callable(destroy_method):
                    if asyncio.iscoroutinefunction(destroy_method):
                        await destroy_method()
                    else:
                        destroy_method()
                    logger.debug(f"Called destroy method on bean '{bean_name}'")

            # 如果Bean实现了上下文管理器协议
            if hasattr(bean_instance, "__aexit__"):
                await bean_instance.__aexit__(None, None, None)
                logger.debug(f"Called async context manager exit on bean '{bean_name}'")
            elif hasattr(bean_instance, "__exit__"):
                bean_instance.__exit__(None, None, None)
                logger.debug(f"Called context manager exit on bean '{bean_name}'")

        except Exception as e:
            logger.warning(f"Error destroying bean '{bean_name}': {e}")

    async def _shutdown_event_publisher(self) -> None:
        """关闭事件发布器"""
        try:
            if hasattr(self._event_publisher, "shutdown"):
                shutdown_method = self._event_publisher.shutdown
                if callable(shutdown_method):
                    if asyncio.iscoroutinefunction(shutdown_method):
                        await shutdown_method()
                    else:
                        shutdown_method()
                    logger.debug("Event publisher shutdown")
        except Exception as e:
            logger.warning(f"Failed to shutdown event publisher: {e}")

    async def _cleanup_resources(self) -> None:
        """清理资源"""
        try:
            # 清理Bean工厂缓存
            if hasattr(self._bean_factory, "clear_cache"):
                self._bean_factory.clear_cache()

            # 清理环境配置缓存
            if hasattr(self._environment, "clear_cache"):
                self._environment.clear_cache()

            logger.debug("Resources cleaned up")
        except Exception as e:
            logger.warning(f"Failed to cleanup resources: {e}")

    async def _execute_shutdown_sequence(self) -> None:
        """执行完整的停止序列

        停止过程分为三个阶段:
        1. 准备阶段:发布事件、执行前置钩子
        2. 核心阶段:停止模块和组件
        3. 清理阶段:清理资源、执行后置钩子
        """
        logger.debug("Starting shutdown sequence...")

        try:
            await self._prepare_shutdown()
            await self._execute_core_shutdown()
            await self._finalize_shutdown()
            logger.debug("Shutdown sequence completed successfully")
        except Exception as e:
            logger.error(f"Error during shutdown sequence: {e}")
            raise

    async def _prepare_shutdown(self) -> None:
        """准备关闭阶段

        执行关闭前的准备工作
        """
        # 1. 发布停止开始事件
        await self._publish_stop_start_event()

        # 2. 执行自定义停止钩子(前置)
        await self._execute_shutdown_hooks("pre")

        # 3. 关闭Actuator集成(在模块停止前)
        await self._shutdown_actuator_integration()

    async def _execute_core_shutdown(self) -> None:
        """执行核心关闭逻辑

        停止所有核心组件和服务
        """
        # 4. 停止所有模块(统一模块停止)
        await self._stop_all_modules()

        # 5. 等待正在执行的任务完成(可选,避免复杂性)
        # await self._wait_for_active_tasks()

        # 6. 停止健康检查集成
        await self._stop_health_integration()

        # 7. 停止生命周期组件
        await self._stop_lifecycle_components()

        # 8. 销毁单例Bean(按依赖顺序逆序)
        await self._destroy_singleton_beans()

        # 9. 停止线程池和异步任务
        await self._stop_thread_pools()

    async def _finalize_shutdown(self) -> None:
        """完成关闭阶段

        执行最终的清理和事件发布
        """
        # 10. 发布停止完成事件(在关闭事件发布器之前)
        await self._publish_stop_event()

        # 11. 关闭事件发布器
        await self._shutdown_event_publisher()

        # 12. 执行自定义停止钩子(后置)
        await self._execute_shutdown_hooks("post")

        # 13. 清理资源和缓存
        await self._cleanup_resources()

    async def _shutdown_actuator_integration(self) -> None:
        """关闭监控集成 - 接口驱动版本"""
        try:
            # 新的监控集成
            if hasattr(self, "_monitoring_integration") and self._monitoring_integration:
                await self._monitoring_integration.shutdown_monitoring()
                logger.debug("Monitoring integration shutdown completed")

            # 向后兼容：旧的 Actuator 集成
            if hasattr(self, "_actuator_integration") and self._actuator_integration:
                await self._actuator_integration.shutdown_actuator()
                logger.debug("Legacy actuator integration shutdown completed")
        except Exception as e:
            logger.warning(f"Failed to shutdown monitoring integration: {e}")

    async def _force_shutdown(self) -> None:
        """强制停止(用于超时或异常情况)"""
        logger.warning("Executing force shutdown...")

        try:
            # 强制停止所有组件,忽略错误
            tasks = [
                self._force_stop_scheduled_tasks(),
                self._force_stop_web_server(),
                self._force_stop_thread_pools(),
                self._force_cleanup_resources(),
            ]

            # 并发执行强制停止,设置较短超时
            await asyncio.gather(*tasks, return_exceptions=True)

        except Exception as e:
            logger.error(f"Error during force shutdown: {e}")

    def add_shutdown_hook(self, hook_func, phase: str = "pre") -> None:
        """添加停止钩子

        Args:
            hook_func: 停止钩子函数,可以是同步或异步函数
            phase: 执行阶段,'pre'(前置)或'post'(后置)
        """
        if phase not in ["pre", "post"]:
            raise ValueError("Phase must be 'pre' or 'post'")

        self._shutdown_hooks.append({"func": hook_func, "phase": phase})
        logger.debug(f"Added shutdown hook for {phase} phase")

    def remove_shutdown_hook(self, hook_func) -> bool:
        """移除停止钩子

        Args:
            hook_func: 要移除的钩子函数

        Returns:
            bool: 是否成功移除
        """
        original_count = len(self._shutdown_hooks)
        self._shutdown_hooks = [hook for hook in self._shutdown_hooks if hook["func"] != hook_func]
        removed = len(self._shutdown_hooks) < original_count

        if removed:
            logger.debug("Removed shutdown hook")

        return removed

    async def _publish_stop_start_event(self) -> None:
        """发布停止开始事件"""
        try:
            from miniboot.events.types import ApplicationStoppingEvent

            stopping_event = ApplicationStoppingEvent(application=self)
            await self.publish_event_async(stopping_event)
            logger.debug("Application stopping event published")
        except Exception as e:
            logger.warning(f"Failed to publish stopping event: {e}")

    async def _execute_shutdown_hooks(self, phase: str) -> None:
        """执行停止钩子

        Args:
            phase: 执行阶段,'pre' 或 'post'
        """
        hooks = [hook for hook in self._shutdown_hooks if hook["phase"] == phase]

        if not hooks:
            return

        logger.debug(f"Executing {len(hooks)} shutdown hooks for {phase} phase")

        for hook in hooks:
            try:
                hook_func = hook["func"]
                if asyncio.iscoroutinefunction(hook_func):
                    await hook_func()
                else:
                    hook_func()
                logger.debug(f"Executed shutdown hook: {hook_func.__name__}")
            except Exception as e:
                logger.warning(f"Shutdown hook failed: {e}")

    async def _stop_scheduled_tasks(self) -> None:
        """停止定时任务"""
        try:
            # 检查是否有定时任务调度器
            if hasattr(self, "_scheduler") and self._scheduler:
                logger.debug("Stopping scheduled tasks...")
                if hasattr(self._scheduler, "shutdown"):
                    await self._scheduler.shutdown(wait=True)
                    logger.debug("Scheduled tasks stopped")

            # 也检查Bean工厂中的调度器
            if hasattr(self._bean_factory, "_scheduler"):
                scheduler = self._bean_factory._scheduler
                if scheduler and hasattr(scheduler, "shutdown"):
                    await scheduler.shutdown(wait=True)
                    logger.debug("Bean factory scheduler stopped")

        except Exception as e:
            logger.warning(f"Failed to stop scheduled tasks: {e}")

    async def _force_stop_scheduled_tasks(self) -> None:
        """强制停止定时任务"""
        try:
            if hasattr(self, "_scheduler") and self._scheduler and hasattr(self._scheduler, "shutdown"):
                await self._scheduler.shutdown(wait=False)

            if hasattr(self._bean_factory, "_scheduler"):
                scheduler = self._bean_factory._scheduler
                if scheduler and hasattr(scheduler, "shutdown"):
                    await scheduler.shutdown(wait=False)

        except Exception as e:
            logger.debug(f"Force stop scheduled tasks error (ignored): {e}")

    async def _stop_web_server(self) -> None:
        """停止Web服务器"""
        try:
            # 检查是否有Web服务器实例
            if hasattr(self, "_web_server") and self._web_server:
                logger.debug("Stopping web server...")
                if hasattr(self._web_server, "shutdown"):
                    await self._web_server.shutdown()
                    logger.debug("Web server stopped")

        except Exception as e:
            logger.warning(f"Failed to stop web server: {e}")

    async def _force_stop_web_server(self) -> None:
        """强制停止Web服务器"""
        try:
            if hasattr(self, "_web_server") and self._web_server:
                if hasattr(self._web_server, "force_shutdown"):
                    await self._web_server.force_shutdown()
                elif hasattr(self._web_server, "shutdown"):
                    await self._web_server.shutdown()

        except Exception as e:
            logger.debug(f"Force stop web server error (ignored): {e}")

    async def _wait_for_active_tasks(self, timeout: float = 5.0) -> None:
        """等待活跃任务完成

        Args:
            timeout: 等待超时时间(秒)
        """
        try:
            logger.debug("Waiting for active tasks to complete...")

            # 获取当前事件循环中的所有任务,排除当前任务和已完成的任务
            current_task = asyncio.current_task()
            all_tasks = []

            for task in asyncio.all_tasks():
                # 排除当前任务、已完成的任务、以及停止相关的任务
                if task != current_task and not task.done() and not task.cancelled() and not getattr(task, "_is_shutdown_task", False):
                    all_tasks.append(task)

            if all_tasks:
                logger.debug(f"Waiting for {len(all_tasks)} active tasks...")
                try:
                    # 使用wait而不是gather,避免取消传播
                    done, pending = await asyncio.wait(all_tasks, timeout=timeout, return_when=asyncio.ALL_COMPLETED)

                    if pending:
                        logger.warning(f"{len(pending)} tasks did not complete within {timeout} seconds")
                        # 温和地取消未完成的任务
                        for task in pending:
                            if not task.done():
                                task.cancel()
                                with suppress(asyncio.CancelledError, asyncio.TimeoutError):
                                    await asyncio.wait_for(task, timeout=1.0)
                    else:
                        logger.debug("All active tasks completed")

                except Exception as e:
                    logger.warning(f"Error waiting for tasks: {e}")
            else:
                logger.debug("No active tasks to wait for")

        except Exception as e:
            logger.warning(f"Error waiting for active tasks: {e}")

    async def _stop_thread_pools(self) -> None:
        """停止线程池和异步模块"""
        try:
            # 1. 停止异步模块(如果已集成)
            await self._shutdown_async_module()

            # 2. 检查应用上下文中的线程池管理器
            if hasattr(self, "_thread_pool_manager"):
                logger.debug("Stopping application context thread pools...")
                await self._thread_pool_manager.shutdown()
                logger.debug("Application context thread pools stopped")

            # 3. 检查Bean工厂中的线程池
            if hasattr(self._bean_factory, "_thread_pools"):
                thread_pools = self._bean_factory._thread_pools
                for pool_name, pool in thread_pools.items():
                    if hasattr(pool, "shutdown"):
                        pool.shutdown(wait=True)
                        logger.debug(f"Bean factory thread pool '{pool_name}' stopped")

        except Exception as e:
            logger.warning(f"Failed to stop thread pools: {e}")

    async def _shutdown_async_module(self) -> None:
        """关闭异步模块"""
        try:
            # 检查异步模块是否已集成（通过检查Bean工厂中的异步处理器）
            bean_factory = self.get_bean_factory()
            async_enabled = hasattr(bean_factory, "_post_processors") and any(
                "async" in str(type(processor)).lower() for processor in getattr(bean_factory, "_post_processors", [])
            )

            if not async_enabled:
                logger.debug("Async module not integrated, skipping shutdown")
                return

            logger.debug("Shutting down async module...")

            # 异步模块使用懒加载模式，无需特殊关闭逻辑
            # 线程池等资源会在应用关闭时自动清理

            logger.debug("Async module shutdown completed")

        except Exception as e:
            logger.error(f"Failed to shutdown async module: {e}")
            # 异步模块关闭失败不应该阻止应用关闭

    async def _force_stop_thread_pools(self) -> None:
        """强制停止线程池"""
        try:
            if hasattr(self, "_thread_pool_manager"):
                await self._thread_pool_manager.force_shutdown()

            if hasattr(self._bean_factory, "_thread_pools"):
                thread_pools = self._bean_factory._thread_pools
                for _pool_name, pool in thread_pools.items():
                    if hasattr(pool, "shutdown"):
                        pool.shutdown(wait=False)

        except Exception as e:
            logger.debug(f"Force stop thread pools error (ignored): {e}")

    async def _force_cleanup_resources(self) -> None:
        """强制清理资源"""
        try:
            # 强制清理Bean工厂缓存
            if hasattr(self._bean_factory, "clear_cache"):
                self._bean_factory.clear_cache()

            # 强制清理环境配置缓存
            if hasattr(self._environment, "clear_cache"):
                self._environment.clear_cache()

            # 清理停止钩子
            self._shutdown_hooks.clear()

        except Exception as e:
            logger.debug(f"Force cleanup resources error (ignored): {e}")

    def get_shutdown_hooks_count(self) -> int:
        """获取停止钩子数量

        Returns:
            int: 停止钩子数量
        """
        return len(self._shutdown_hooks)

    async def _initialize_health_integration(self) -> None:
        """初始化健康检查集成"""
        try:
            # 检查是否启用了健康检查集成
            health_enabled = self._environment.get_property("management.health.integration.enabled", True)

            if not health_enabled:
                logger.debug("Health integration is disabled")
                return

            # 导入健康检查扩展
            from .actuator_health_extension import enable_actuator_health_integration

            # 启用健康检查集成
            await enable_actuator_health_integration(self, auto_start=True)

            logger.info("✅ Health integration initialized successfully")

        except ImportError as e:
            logger.debug(f"Actuator health integration not available: {e}")
        except Exception as e:
            logger.warning(f"Failed to initialize health integration: {e}")

    async def _stop_health_integration(self) -> None:
        """停止健康检查集成"""
        try:
            # 导入健康检查扩展
            from .actuator_health_extension import disable_actuator_health_integration

            # 禁用健康检查集成
            await disable_actuator_health_integration(self)

            logger.debug("Health integration stopped")

        except ImportError:
            logger.debug("Actuator health integration not available")
        except Exception as e:
            logger.warning(f"Failed to stop health integration: {e}")

    def get_health_status(self) -> dict:
        """获取应用上下文健康状态

        Returns:
            dict: 健康状态信息
        """
        try:
            from .actuator_health_extension import get_actuator_health_status

            return get_actuator_health_status(self)
        except ImportError:
            return {"status": "UNKNOWN", "message": "Health integration not available"}
        except Exception as e:
            return {"status": "DOWN", "message": f"Health check failed: {str(e)}"}

    def is_health_integration_enabled(self) -> bool:
        """检查是否启用了健康检查集成

        Returns:
            bool: 如果启用了健康检查集成返回True,否则返回False
        """
        try:
            from .actuator_health_extension import is_actuator_health_enabled

            return is_actuator_health_enabled(self)
        except ImportError:
            return False
        except Exception:
            return False

    # ==================== 智能功能方法 ====================

    def is_async_mode(self) -> bool:
        """检查是否处于异步模式

        Returns:
            bool: 如果处于异步模式返回True
        """
        return self._is_async_mode

    def get_runtime_detector(self) -> RuntimeDetector:
        """获取运行时环境检测器

        Returns:
            RuntimeDetector: 运行时环境检测器实例
        """
        return self._runtime_detector

    def enable_auto_detect(self, enabled: bool = True) -> None:
        """启用或禁用自动检测

        Args:
            enabled: 是否启用自动检测
        """
        self._auto_detect = enabled
        if enabled and not self._async_mode_detected:
            self._is_async_mode = self._runtime_detector.is_async_environment()
            self._async_mode_detected = True
            logger.debug(f"Auto-detect enabled, detected async mode: {self._is_async_mode}")

    def force_async_mode(self, async_mode: bool) -> None:
        """强制设置异步模式

        Args:
            async_mode: 是否强制异步模式
        """
        self._is_async_mode = async_mode
        self._async_mode_detected = True
        self._auto_detect = False  # 禁用自动检测
        logger.info(f"Forced async mode: {async_mode}")

    def create_smart_context(self) -> "DefaultApplicationContext":
        """创建智能上下文(向后兼容方法)

        Returns:
            DefaultApplicationContext: 自身实例,因为现在默认就是智能的
        """
        logger.debug("create_smart_context called - returning self as DefaultApplicationContext is now smart by default")
        return self

    def register_bean_post_processor(self, processor) -> None:
        """注册Bean后置处理器

        Args:
            processor: Bean后置处理器实例

        Raises:
            RuntimeError: 如果Bean工厂不支持后置处理器
        """
        if not hasattr(self._bean_factory, "add_processor"):
            raise RuntimeError("Bean factory does not support post processors")

        self._bean_factory.add_processor(processor)
        logger.debug(f"Registered bean post processor: {processor.__class__.__name__}")
