{
    "python.defaultInterpreterPath": "${workspaceFolder}/.venv/Scripts/python.exe",
    "python.terminal.activateEnvironment": true,
    "python.terminal.activateEnvInCurrentTerminal": true,
    "python.linting.enabled": true,
    "python.linting.ruffEnabled": true,
    "python.linting.pylintEnabled": false,
    "python.linting.flake8Enabled": false,
    "python.linting.mypyEnabled": true,
    "python.linting.mypyArgs": [
        "--show-error-codes",
        "--pretty",
        "--show-column-numbers",
        "--follow-imports=normal",
        "--ignore-missing-imports"
    ],
    "python.formatting.provider": "ruff",
    "ruff.configurationPreference": "filesystemFirst",
    "python.testing.pytestEnabled": false,
    "python.testing.unittestEnabled": true,
    "python.testing.unittestArgs": [
        "-v",
        "-s",
        "./tests",
        "-p",
        "*_test.py"
    ],
    "python.testing.autoTestDiscoverOnSaveEnabled": true,
    "files.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true,
        ".venv": false,
        "*.egg-info": true,
        ".pytest_cache": true,
        ".coverage": true,
        "htmlcov": true
    },
    "files.watcherExclude": {
        "**/.venv/**": true,
        "**/node_modules/**": true,
        "**/__pycache__/**": true
    },
    "editor.formatOnSave": true,
    "editor.formatOnPaste": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": "explicit",
        "source.fixAll.ruff": "explicit"
    },

    // 代码质量设置
    "files.trimTrailingWhitespace": true,
    "files.insertFinalNewline": true,
    "files.trimFinalNewlines": true,
    "editor.tabSize": 4,
    "editor.insertSpaces": true,
    "editor.detectIndentation": false,
    "editor.rulers": [150],
    "editor.renderWhitespace": "trailing",
    "editor.trimAutoWhitespace": true,
    "editor.wordWrap": "off",
    "python.analysis.typeCheckingMode": "strict",
    "python.analysis.autoImportCompletions": true,
    "python.analysis.autoSearchPaths": true,
    "python.analysis.extraPaths": ["./miniboot"],
    "python.analysis.indexing": true,
    "python.analysis.packageIndexDepths": [
        {"name": "miniboot", "depth": 10},
        {"name": "", "depth": 2}
    ],
    "python.analysis.userFileIndexingLimit": -1,
    "python.analysis.stubPath": "./typings",
    "python.analysis.useLibraryCodeForTypes": true,
    "python.analysis.completeFunctionParens": true,
    "python.analysis.inlayHints.variableTypes": true,
    "python.analysis.inlayHints.functionReturnTypes": true,
    "python.analysis.inlayHints.callArgumentNames": "partial",
    "python.analysis.inlayHints.pytestParameters": true,

    // 增强的类型提示和智能感知
    "python.analysis.supportDocstringTemplate": true,
    "python.analysis.gotoDefinitionInStringLiteral": true,
    "python.analysis.importFormat": "absolute",
    "python.analysis.addImport.exactMatchOnly": false,
    "python.analysis.highlight.enabled": true,

    // 错误和警告显示增强
    "problems.decorations.enabled": true,
    "problems.showCurrentInStatus": true,
    "editor.parameterHints.enabled": true,
    "editor.parameterHints.cycle": true,
    "editor.suggest.showStatusBar": true,
    "editor.suggest.preview": true,
    "editor.suggest.showTypeParameters": true,
    "editor.hover.enabled": true,
    "editor.hover.delay": 300,
    "python.analysis.logLevel": "Information",
    "python.analysis.diagnosticMode": "workspace",
    "python.analysis.autoFormatStrings": true,
    "python.analysis.fixAll": ["source.unusedImports"],
    "python.analysis.diagnosticSeverityOverrides": {
        // 关键类型检查 - 这些是Cursor显示的主要问题
        "reportUnknownParameterType": "error",
        "reportUnknownArgumentType": "error",
        "reportUnknownLambdaType": "error",
        "reportUnknownVariableType": "error",
        "reportUnknownMemberType": "error",
        "reportMissingParameterType": "error",
        "reportMissingTypeArgument": "error",
        "reportMissingTypeStubs": "warning",
        "reportIncompleteStub": "warning",

        // 函数和方法类型检查
        "reportIncompatibleMethodOverride": "error",
        "reportIncompatibleVariableOverride": "error",
        "reportInconsistentConstructor": "error",
        "reportOverlappingOverload": "error",
        "reportNoReturnInFunction": "error",
        "reportFunctionMemberAccess": "error",
        "reportUntypedFunctionDecorator": "error",
        "reportUntypedClassDecorator": "error",
        "reportUntypedBaseClass": "error",
        "reportUntypedNamedTuple": "error",

        // 严格的类型赋值检查
        "reportGeneralTypeIssues": "error",
        "reportAssignmentType": "error",
        "reportArgumentType": "error",
        "reportReturnType": "error",
        "reportOperatorIssue": "error",
        "reportIndexIssue": "error",
        "reportCallIssue": "error",
        "reportAttributeAccessIssue": "error",

        // Optional 类型检查
        "reportOptionalSubscript": "error",
        "reportOptionalMemberAccess": "error",
        "reportOptionalCall": "error",
        "reportOptionalIterable": "error",
        "reportOptionalContextManager": "error",
        "reportOptionalOperand": "error",

        // 导入和模块检查
        "reportMissingImports": "error",
        "reportImportCycles": "error",
        "reportUnusedImport": "warning",
        "reportDuplicateImport": "error",
        "reportPrivateImportUsage": "none",

        // 变量和作用域检查
        "reportUndefinedVariable": "error",
        "reportUnboundVariable": "error",
        "reportUnusedVariable": "warning",
        "reportUnusedClass": "warning",
        "reportUnusedFunction": "warning",
        "reportRedeclaration": "error",

        // 抽象类和接口检查
        "reportAbstractUsage": "error",
        "reportAssertTypeFailure": "error",
        "reportInvalidTypeVarUse": "error",
        "reportTypeCommentUsage": "error",

        // 代码质量检查（保持warning级别）
        "reportConstantRedefinition": "warning",
        "reportInvalidStringEscapeSequence": "warning",
        "reportSelfClsParameterName": "warning",
        "reportImplicitStringConcatenation": "warning",
        "reportCallInDefaultInitializer": "warning",
        "reportAssertAlwaysTrue": "warning",
        "reportShadowedImports": "warning",
        "reportDeprecated": "warning",

        // 保持none的设置（避免过多噪音）
        "reportUnnecessaryTypeIgnoreComment": "none",
        "reportUnnecessaryComparison": "none",
        "reportUnnecessaryContains": "none",
        "reportUnnecessaryIsInstance": "none",
        "reportUnnecessaryPass": "none",
        "reportUnnecessaryCast": "warning",
        "reportUnusedCallResult": "none",
        "reportPrivateUsage": "none",
        "reportTypeIgnoreComment": "warning",
        "reportWildcardImportFromLibrary": "warning",
        "reportMissingModuleSource": "warning"
    },
    "[python]": {
        "editor.defaultFormatter": "charliermarsh.ruff",
        "editor.tabSize": 4,
        "editor.insertSpaces": true,
        "editor.formatOnSave": true,
        "editor.codeActionsOnSave": {
            "source.fixAll": "explicit",
            "source.organizeImports": "explicit"
        }
    },


    // Terminal environment configuration for Windows
    "terminal.integrated.env.windows": {
        "PYTHONPATH": "${workspaceRoot}"
    },

    // Code Runner configuration
    "code-runner.runInTerminal": true,
    "code-runner.respectShebang": false,
    "code-runner.saveFileBeforeRun": true,
    "code-runner.fileDirectoryAsCwd": true,
    "code-runner.clearPreviousOutput": true,
    "code-runner.executorMap": {
        "python": "$env:PYTHONIOENCODING='utf8'; $env:PYTHONPATH='${workspaceRoot}'; & '${workspaceFolder}\\.venv\\Scripts\\python.exe' -u $fullFileName"
    }
}
